package cn.harmonycloud.trinasolar.model.entity;


import cn.harmonycloud.common.core.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * @Description 模版变量表
 * <AUTHOR> Gong
 * @Date 2025/3/25
 **/
@TableName(value ="template_variable")
@Data
public class TemplateVariable extends BaseEntity {

    @TableId(type = IdType.AUTO)
    private Long id;

    private Long templateId;

    private String variableName;

    private String variableType;


    private String defaultValue;

    private String required;

    private String description;



}
