package cn.harmonycloud.trinasolar.model.entity;


import cn.harmonycloud.common.core.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.ibatis.type.JdbcType;

import java.util.List;

/**
 * @Description 应用程序表
 * <AUTHOR> Gong
 * @Date 2025/3/25
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "application_program", autoResultMap = true)
public class ApplicationProgram extends BaseEntity {

    @TableId(type = IdType.AUTO)
    private Long id;


    /**
     * 应用程序的唯一标识
     */
    private Long applicationId;

    /**
     * 应用系统id
     */
    private String systemId;

    /**
     * 应用程序代码
     */
    private String programCode;

    /**
     * 应用程序中文名称
     */
    private String programNameCn;

    /**
     * 应用程序英文名称
     */
    private String programNameEn;


    /**
     * 初始化状态（0:等待初始化 1:已初始化）
     */
    private String initStatus; // 0:等待初始化 1:已初始化

    /**
     * 应用程序当前运行的版本号
     */
    private String currentRunningVersion;

    /**
     * 应用程序状态（0:未开始 1:开发阶段 2:测试阶段 3:发布阶段 4:生产阶段 5:已废弃）
     */
    private String programStatus;// 0:未开始 1:开发阶段 2:测试阶段 3:发布阶段 4:生产阶段 5:已废弃

    /**
     * 应用程序的技术特征标签
     */
    private String technicalStackTags;


    /**
     * 技术栈类型，前端或者后端
     */
    private String technologyStack;


    /**
     * 应用程序的健康程度指标
     */
    private String health;


    /**
     * 技术负责人
     */
    @TableField(typeHandler = JacksonTypeHandler.class, jdbcType = JdbcType.VARCHAR)
    @JsonDeserialize(contentAs = Long.class)
    private List<Long> developDirectorId;


    /**
     * 应用程序描述
     */
    private String programDescCn;


    /**
     * gitlab代码仓库
     */
    private String gitlabRepoUrl;


    /**
     * 脚手架模版id
     */
    private String scaffoldTemplateId;


}
