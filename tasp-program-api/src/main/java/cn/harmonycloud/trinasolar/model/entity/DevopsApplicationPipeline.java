package cn.harmonycloud.trinasolar.model.entity;


import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
@TableName("devops_application_pipeline")
@Accessors(chain = true)
public class DevopsApplicationPipeline {
    @TableId(type = IdType.AUTO)
    private Long id;
    
    private Long applicationId;
    private Long programId;
    private String pipelineId;
    private String environment;
    private String pipelineName;
    private LocalDateTime pipelineCreateTime;
    private String runningStatus;
    private String buildUser;

    @TableField(fill = FieldFill.INSERT)
    private Long createBy;
    
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updateBy;
    
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @TableLogic
    private String delFlag;
}