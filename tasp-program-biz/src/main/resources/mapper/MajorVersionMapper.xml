<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.harmonycloud.development.outbound.db.mapper.MajorVersionMapper">

    <select id="versionSubsystemQuery" resultType="cn.harmonycloud.development.pojo.entity.VersionSubsystem">
        SELECT
            v.id versionId,
            s.id subsystemId,
            s.system_id systemId,
            v.version_number versionNumber,
            v.director_id versionDirectorId,
--             v.version_status versionStatus,
            s.full_name_cn fullNameCn,
            s.sub_code subCode,
            s.sub_desc_cn subDescCn,
            s.tech_director_id techDirectorId,
            s.technology technology
        FROM
            major_version v
                INNER JOIN
            devops_sub_system s ON v.sub_system_id = s.id
        WHERE
            v.del_flag = 0 AND s.del_flag = 0
        <if test="req.ids != null and req.ids.size() != 0">
            and v.id in
            <foreach collection="req.ids" item="item1" open="(" close=")" index="i" separator=",">
                #{item1}
            </foreach>
        </if>
        <if test="req.systemId != null">
            and s.system_id = #{req.systemId}
        </if>
        <if test="req.subDirectorId != null">
            and s.tech_director_id = #{req.subDirectorId}
        </if>
        <if test="req.search != null and req.search != ''">
            and (s.full_name_cn like concat('%',#{req.search},'%') or v.version_number like concat('%',#{req.search},'%'))
        </if>

    </select>
</mapper>