<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.harmonycloud.development.outbound.db.mapper.FeatureBranchMapper">

    <resultMap id="BaseResultMap" type="cn.harmonycloud.development.pojo.entity.FeatureBranch">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="branchName" column="branch_name" jdbcType="VARCHAR"/>
            <result property="branchDevName" column="branch_dev_name" jdbcType="VARCHAR"/>
            <result property="codeName" column="code_name" jdbcType="VARCHAR"/>
            <result property="featureId" column="feature_id" jdbcType="BIGINT"/>
            <result property="subSystemId" column="sub_system_id" jdbcType="BIGINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="createBy" column="create_by" jdbcType="BIGINT"/>
            <result property="delFlag" column="del_flag" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,branch_id,branch_name,
        branch_dev_name,code_name,feature_id,
        sub_system_id,create_time,create_by,
        del_flag
    </sql>
</mapper>
