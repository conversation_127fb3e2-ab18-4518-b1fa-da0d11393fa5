<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.harmonycloud.development.outbound.db.mapper.DevopsSystemMapper">

    <select id="listByIdsIgnoreTenant" resultType="cn.harmonycloud.development.pojo.entity.DevopsSystem">
        select * from devops_system where id in
        <foreach collection="systemIds" item="systemId" open="(" close=")" separator=",">
            #{systemId}
        </foreach>
    </select>

    <select id="listByProject" resultType="cn.harmonycloud.development.pojo.entity.DevopsSystem">
        select * from devops_system s
        <if test="projectId != null">
        join system_project sp on s.id=sp.system_id
        where sp.project_id=#{projectId}
        </if>
    </select>

</mapper>