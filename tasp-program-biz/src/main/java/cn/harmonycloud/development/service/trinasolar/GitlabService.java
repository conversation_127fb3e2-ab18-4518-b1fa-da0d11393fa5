package cn.harmonycloud.development.service.trinasolar;

import cn.harmonycloud.development.config.GitSyncDTO;
import cn.harmonycloud.development.constant.GitlabAccessLevelEnum;
import cn.harmonycloud.development.execption.thgn.GitException;
import cn.harmonycloud.development.outbound.util.GitFileUtil;
import cn.harmonycloud.development.pojo.dto.thgn.CommitStatistics;
import cn.harmonycloud.development.pojo.dto.thgn.GitlabCommitCountDTO;
import cn.harmonycloud.development.pojo.dto.thgn.ProjectCommitInfoDTO;
import cn.harmonycloud.enums.ExceptionCode;
import cn.harmonycloud.trinasolar.model.entity.ScaffoldComponent;
import cn.harmonycloud.trinasolar.model.entity.ScaffoldTemplate;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpStatus;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.eclipse.jgit.api.Git;
import org.eclipse.jgit.transport.UsernamePasswordCredentialsProvider;
import org.eclipse.jgit.util.FileUtils;
import org.gitlab4j.api.GitLabApi;
import org.gitlab4j.api.GitLabApiException;
import org.gitlab4j.api.models.Project;
import org.gitlab4j.api.models.Visibility;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.net.URI;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class GitlabService {

    @Value("${trinasolar.git.url}")
    protected String gitUrl;
    @Value("${trinasolar.git.token}")
    protected String gitToken;

    public static String BRANCH_NAME = "master";

    public GitLabApi getGitLabApi() {
        return new GitLabApi(gitUrl, gitToken);
    }

    public static final String ORIGIN = "origin";
    public static final String OAUTH2_USERNAME = "oauth2"; // GitLab要求的固定用户名
    public static final String PRIVATE_TOKEN = "PRIVATE-TOKEN";
    public static final String UTF_8 = "UTF-8";
    public static final String PAGE_URL = "%s://%s/api/v4/projects?search=%s&search_namespaces=true&per_page=10000";
    public static final String GIT = ".git";
    public static final String GIT_PULL = "git-pull-";
    public static final String GIT_PUSH = "git-push-";
    public static final String API_PROJECTS_BY_GROUP = "%s/api/v4/groups/%s/projects?per_page=10000&with_shared=false";

    /**
     * 查询子组下的所有Git项目路径（HTTP格式）
     *
     * @param subgroupPath 子组路径（如：parent-group/sub-group）
     * @param privateToken GitLab访问令牌
     * @param gitlabDomain GitLab域名（如：gitlab.example.com）
     * @return 项目Git路径列表（HTTP格式）
     * @throws Exception 请求异常或解析异常
     */
    public static List<String> getSubgroupProjectsGitList(String subgroupPath, String privateToken, String gitlabDomain) {
        // 构造API URL
        String encodedSubgroup = URLEncoder.encode(subgroupPath, StandardCharsets.UTF_8).replace("+", "%20");
        String apiUrl = String.format(API_PROJECTS_BY_GROUP, gitlabDomain, encodedSubgroup);

        HttpGet get = new HttpGet(apiUrl);
        get.setHeader(PRIVATE_TOKEN, privateToken);

        try (CloseableHttpClient httpClient = HttpClients.createDefault();
             CloseableHttpResponse response = httpClient.execute(get)) {

            int statusCode = response.getStatusLine().getStatusCode();
            if (statusCode != HttpStatus.SC_OK) {
                String errorBody = EntityUtils.toString(response.getEntity());
                log.error("查询子组项目失败，状态码：{}，响应：{}", statusCode, errorBody);
                throw new GitException("查询子组项目失败，状态码：" + statusCode);

            }

            String responseBody = EntityUtils.toString(response.getEntity());
            JSONArray projects = JSON.parseArray(responseBody);
            return projects.stream()
                    .map(JSONObject.class::cast)
                    .map(project -> project.getString("http_url_to_repo"))
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("获取组下用户异常！", e);
            //todo 临时处理，不让前端报错
            // throw new GitException("获取组下用户异常！" + e.getMessage());
            return new ArrayList<>();
        }
    }


    /**
     * 统计多个GitLab仓库URL下单个用户的总提交次数和首次提交时间
     *
     * @param gitlabDomain GitLab域名
     * @param privateToken GitLab访问令牌
     * @param gitUrls      GitLab仓库URL列表
     * @param username     用户名
     * @param startTime    开始时间（格式：yyyy-MM-dd HH:mm:ss）
     * @param endTime      结束时间（格式：yyyy-MM-dd HH:mm:ss）
     * @return 提交统计信息，包含总提交次数和首次提交时间
     */
    public static ProjectCommitInfoDTO getUserTotalCommitStatsByGitUrls(
            String gitlabDomain, String privateToken, List<String> gitUrls, String username,
            String startTime, String endTime) {
        // 参数校验
        if (StringUtils.isEmpty(gitlabDomain) || StringUtils.isEmpty(privateToken) || CollectionUtils.isEmpty(gitUrls) || StringUtils.isEmpty(username)) {
            log.error("参数错误：GitLab域名、访问令牌、仓库URL列表或用户名不能为空");
            throw new IllegalArgumentException("参数错误：GitLab域名、访问令牌、仓库URL列表或用户名不能为空");
        }

        // 时间格式校验与转换
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date start = null;
        Date end = null;
        try {
            if (StringUtils.isNotEmpty(startTime)) {
                start = sdf.parse(startTime);
            }
            if (StringUtils.isNotEmpty(endTime)) {
                end = sdf.parse(endTime);
            }
        } catch (ParseException e) {
            log.error("时间格式错误：{}", e.getMessage());
            throw new IllegalArgumentException("时间格式错误，请使用yyyy-MM-dd HH:mm:ss格式");
        }

        // 创建线程安全的计数器和时间变量
        final AtomicInteger totalCommitCount = new AtomicInteger(0);
        final AtomicReference<Date> earliestCommitTime = new AtomicReference<>(null);

        // 创建线程池，线程数可根据实际需求调整
        int threadPoolSize = Math.min(gitUrls.size(), 10); // 最多10个线程
        ExecutorService executorService = Executors.newFixedThreadPool(threadPoolSize);

        try {
            // 并行处理每个Git URL
            Date finalStart = start;
            Date finalEnd = end;
            List<CompletableFuture<Void>> futures = gitUrls.stream()
                    .map(gitUrl -> CompletableFuture.runAsync(() -> {
                        try {
                            // 从URL提取项目路径
                            URI uri = URI.create(gitUrl);
                            String projectPath = uri.getPath();
                            if (projectPath.endsWith(".git")) {
                                projectPath = projectPath.substring(0, projectPath.length() - 4);
                            }
                            projectPath = projectPath.substring(1); // 移除开头的斜杠

                            // 构造提交记录API URL
                            StringBuilder apiUrlBuilder = new StringBuilder();
                            apiUrlBuilder.append(gitlabDomain)
                                    .append("/api/v4/projects/").append(URLEncoder.encode(projectPath, StandardCharsets.UTF_8))
                                    .append("/repository/commits?per_page=1000")
                                    .append("&author=").append(URLEncoder.encode(username, StandardCharsets.UTF_8));

                            // 添加时间参数
                            if (finalStart != null) {
                                apiUrlBuilder.append("&since=")
                                        .append(URLEncoder.encode(sdf.format(finalStart), StandardCharsets.UTF_8));
                            }
                            if (finalEnd != null) {
                                apiUrlBuilder.append("&until=")
                                        .append(URLEncoder.encode(sdf.format(finalEnd), StandardCharsets.UTF_8));
                            }

                            String apiUrl = apiUrlBuilder.toString();
                            log.info("查询项目 {} 的提交记录API: {}", projectPath, apiUrl);

                            // 创建HTTP请求
                            HttpGet httpGet = new HttpGet(apiUrl);
                            httpGet.setHeader(PRIVATE_TOKEN, privateToken);
                            httpGet.setHeader("Accept", "application/json");

                            // 执行请求
                            try (CloseableHttpClient httpClient = HttpClients.createDefault();
                                 CloseableHttpResponse response = httpClient.execute(httpGet)) {

                                int statusCode = response.getStatusLine().getStatusCode();
                                if (statusCode != HttpStatus.SC_OK) {
                                    String errorBody = EntityUtils.toString(response.getEntity());
                                    log.error("查询项目 {} 提交记录失败，状态码: {}, 响应: {}", projectPath, statusCode, errorBody);
                                    return;
                                }

                                // 解析响应
                                String responseBody = EntityUtils.toString(response.getEntity());
                                JSONArray commits = JSON.parseArray(responseBody);

                                // 统计提交次数和首次提交时间
                                if (commits != null && !commits.isEmpty()) {
                                    // 增加总提交次数
                                    totalCommitCount.addAndGet(commits.size());

                                    // 查找最早的提交时间
                                    Date urlEarliestCommitTime = null;
                                    for (Object commitObj : commits) {
                                        JSONObject commit = (JSONObject) commitObj;
                                        String committedDateStr = commit.getString("committed_date");
                                        try {
                                            SimpleDateFormat gitlabSdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSX");
                                            Date committedDate = gitlabSdf.parse(committedDateStr);
                                            if (urlEarliestCommitTime == null || committedDate.before(urlEarliestCommitTime)) {
                                                urlEarliestCommitTime = committedDate;
                                            }
                                        } catch (ParseException e) {
                                            log.error("解析提交时间失败: {}", e.getMessage());
                                        }
                                    }

                                    // 更新全局最早提交时间
                                    if (urlEarliestCommitTime != null) {
                                        Date finalUrlEarliestCommitTime = urlEarliestCommitTime;
                                        earliestCommitTime.updateAndGet(current -> {
                                            if (current == null || finalUrlEarliestCommitTime.before(current)) {
                                                return finalUrlEarliestCommitTime;
                                            } else {
                                                return current;
                                            }
                                        });
                                    }
                                }
                            } catch (IOException e) {
                                log.error("查询项目 {} 提交记录IO异常: {}", projectPath, e.getMessage());
                            }
                        } catch (Exception e) {
                            log.error("处理项目提交记录异常: {}", e.getMessage());
                        }
                    }, executorService))
                    .toList();

            // 等待所有任务完成
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

        } finally {
            // 关闭线程池
            executorService.shutdown();
            try {
                if (!executorService.awaitTermination(1, TimeUnit.MINUTES)) {
                    executorService.shutdownNow();
                }
            } catch (InterruptedException e) {
                executorService.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
//        if (totalCommitCount.get() == 0) {
//            return null;
//        }
        // 创建并返回结果对象
        ProjectCommitInfoDTO result = new ProjectCommitInfoDTO();
        result.setCommitCount(totalCommitCount.get());
        result.setFirstCommitTime(earliestCommitTime.get());
        return result;
    }


    public static List<String> getSubgroupProjectsIdsList(String subgroupPath, String privateToken, String gitlabDomain) {
        // 构造API URL
        String encodedSubgroup = URLEncoder.encode(subgroupPath, StandardCharsets.UTF_8).replace("+", "%20");
        String apiUrl = String.format(API_PROJECTS_BY_GROUP, gitlabDomain, encodedSubgroup);

        HttpGet get = new HttpGet(apiUrl);
        get.setHeader(PRIVATE_TOKEN, privateToken);

        try (CloseableHttpClient httpClient = HttpClients.createDefault();
             CloseableHttpResponse response = httpClient.execute(get)) {

            int statusCode = response.getStatusLine().getStatusCode();
            if (statusCode != HttpStatus.SC_OK) {
                String errorBody = EntityUtils.toString(response.getEntity());
                log.error("查询子组项目失败，状态码：{}，响应：{}", statusCode, errorBody);
                throw new GitException("查询子组项目失败，状态码：" + statusCode);
            }

            String responseBody = EntityUtils.toString(response.getEntity());
            JSONArray projects = JSON.parseArray(responseBody);
            return projects.stream()
                    .map(JSONObject.class::cast)
                    .map(project -> project.getString("id"))
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("获取组下用户异常！", e);
            return new ArrayList<>();
        }
    }

    /**
     * 创建仓库
     *
     * @param repoBaseUrl      仓库基础地址
     * @param businessName     业务名称
     * @param systemSimpleName 系统简称
     * @param programEnName    项目英文名称
     * @return 创建的项目对象
     */
    public Project createGitlabRepository(String repoBaseUrl, String businessName, String systemSimpleName,
                                          String programEnName) {
        String groupPath = businessName + "/" + systemSimpleName;
        String repoUrl = repoBaseUrl + "/" + groupPath + "/" + programEnName + ".git";
        log.info("create repo url: {}", repoUrl);

        try {
            // 正确设置项目参数
            Project projectParams = new Project()
                    .withName(programEnName)
                    .withPath(programEnName)
                    .withVisibility(Visibility.PRIVATE)
                    .withAutoDevopsEnabled(false)
                    .withInitializeWithReadme(false);
            projectParams.setPathWithNamespace(groupPath);
            // 使用项目对象创建项目，而不是URL字符串
            Project project = getGitLabApi().getProjectApi().createProject(projectParams);
            log.info("create repo success：{}", JSON.toJSONString(project));
            return project;
        } catch (GitLabApiException e) {
            log.error("create repo fail: {}", e.getMessage(), e);
            throw new GitException(ExceptionCode.CREATE_REPOSITORY_FAIL, "create repo fail: " + e.getMessage());
        }
    }

    /**
     * 创建仓库（重载方法，从完整URL解析参数）
     *
     * @param repoUrl 完整的仓库地址
     * @return 创建的项目对象
     */
    public Project createGitlabRepository(String repoUrl) {
        log.info("create repo from url: {}", repoUrl);

        try {
            // 从URL解析项目信息
            URI uri = URI.create(repoUrl);
            String path = uri.getPath();

            // 移除开头的斜杠和结尾的.git
            if (path.startsWith("/")) {
                path = path.substring(1);
            }
            if (path.endsWith(".git")) {
                path = path.substring(0, path.length() - 4);
            }

            // 解析路径：假设格式为 businessName/systemSimpleName/programEnName
            String[] pathParts = path.split("/");
            if (pathParts.length < 3) {
                throw new IllegalArgumentException("Invalid repository URL format. Expected: domain/businessName/systemSimpleName/programEnName");
            }

            String businessName = pathParts[pathParts.length - 3];
            String systemSimpleName = pathParts[pathParts.length - 2];
            String programEnName = pathParts[pathParts.length - 1];

            // 构建基础URL
            String repoBaseUrl = uri.getScheme() + "://" + uri.getHost();
            if (uri.getPort() != -1) {
                repoBaseUrl += ":" + uri.getPort();
            }

            // 调用主要的创建方法
            return createGitlabRepository(repoBaseUrl, businessName, systemSimpleName, programEnName);

        } catch (Exception e) {
            log.error("create repo from url fail: {}", e.getMessage(), e);
            throw new GitException(ExceptionCode.CREATE_REPOSITORY_FAIL, "create repo from url fail: " + e.getMessage());
        }
    }

    // 修改鉴权提供方法
    private static UsernamePasswordCredentialsProvider createTokenCredentials(String privateToken) {
        return new UsernamePasswordCredentialsProvider(OAUTH2_USERNAME, privateToken);
    }


    /**
     * 同步Git代码，从源仓库拉取代码并推送到目标仓库
     *
     * @param gitSyncDTO       同步配置参数
     * @param isTemplate       是否是脚手架
     * @param scaffoldTemplate 脚手架类型
     * @throws Exception
     */
    public void syncGitCode(GitSyncDTO gitSyncDTO, boolean isTemplate, ScaffoldTemplate scaffoldTemplate,
                            List<ScaffoldComponent> scaffoldComponents) throws Exception {
        // 提取参数
        String pullGitUrl = gitSyncDTO.getPullGitUrl();
        String pushGitUrl = gitSyncDTO.getPushGitUrl();
        String pullBranch = gitSyncDTO.getPullBranch();
        String pushBranch = gitSyncDTO.getPushBranch();

        // 创建凭证
        UsernamePasswordCredentialsProvider pullCredentials = createTokenCredentials(gitSyncDTO.getPullPrivateToken());
        UsernamePasswordCredentialsProvider pushCredentials = createTokenCredentials(gitSyncDTO.getPushPrivateToken());

        // 确保目标仓库存在
        createGitlabRepository(pushGitUrl);


        // 创建临时目录
        File tempRoot = new File(System.getProperty("java.io.tmpdir"), "git-sync-" + System.currentTimeMillis());
        tempRoot.mkdirs();
        File pullDir = new File(tempRoot, "source");
        File pushDir = new File(tempRoot, "target");

        try {
            // 克隆源仓库和目标仓库
            Git pullGit = Git.cloneRepository().setURI(pullGitUrl).setBranch(pullBranch).setDirectory(pullDir)
                    .setCredentialsProvider(pullCredentials).setTimeout(30).call();

            Git pushGit = Git.cloneRepository().setURI(pushGitUrl).setBranch(pushBranch).setDirectory(pushDir)
                    .setCredentialsProvider(pushCredentials).setTimeout(30).call();

            try {
                // 清空目标仓库（保留.git目录）
                Arrays.stream(pushDir.listFiles())
                        .filter(file -> !".git".equals(file.getName()))
                        .forEach(file -> {
                            try {
                                FileUtils.delete(file, FileUtils.RECURSIVE);
                            } catch (IOException e) {
                                log.error("清空目标目录失败", e);
                            }
                        });
                // 复制源仓库内容到目标仓库
                copyCodePullToPush(pullDir, pushDir, isTemplate, scaffoldTemplate, gitSyncDTO, scaffoldComponents);
                // 提交并推送
                pushGit.add().addFilepattern(".").call();
                pushGit.commit().setMessage("同步提交 - " + new java.util.Date()).call();
                pushGit.push().setForce(true).setCredentialsProvider(pushCredentials).call();

                log.info("成功同步代码从 {} 到 {}", pullGitUrl, pushGitUrl);
            } finally {
                pullGit.close();
                pushGit.close();
            }
        } finally {
            // 清理临时目录
            FileUtils.delete(tempRoot, FileUtils.RECURSIVE);
        }
    }

    /**
     * 复制源代码到目标目录，并处理前端配置文件中的项目名称替换
     *
     * @param pullDir    源目录
     * @param pushDir    目标目录
     * @param gitSyncDTO 同步配置参数
     * @throws IOException 文件操作异常
     */
    private static void copyCodePullToPush(File pullDir, File pushDir, boolean isTemplate, ScaffoldTemplate scaffoldTemplate,
                                           GitSyncDTO gitSyncDTO, List<ScaffoldComponent> scaffoldComponents) throws IOException {
        String programNameEn = gitSyncDTO.getProgramNameEn();
        String packageName = gitSyncDTO.getPackageName();
        String scaffoldName = scaffoldTemplate.getName();
        String serviceName = scaffoldTemplate.getServiceName();
        Path sourcePath = pullDir.toPath();
        Path targetPath = pushDir.toPath();

        // 用于存储需要重命名的目录映射
        Map<Path, Path> directoriesToRename = new HashMap<>();
        if (isTemplate && "VUE".equals(scaffoldName)) {
            // 前端模板模式：执行文件内容替换和目录重命名
            log.info("使用前端模板模式，将替换项目名称和重命名目录");

            // 第一步：遍历源目录，复制文件并收集需要重命名的目录
            copyFilesAndCollectDirectories(sourcePath, targetPath, programNameEn, directoriesToRename, serviceName);

            // 第二步：重命名收集到的目录
            GitFileUtil.renameDirectories(directoriesToRename);
        } else if (isTemplate && scaffoldTemplate.getAddComponent()) {
            // 后端模板模式：修改后端文件内容和包名
            log.info("使用后端模板模式，将替换项目名称和包名");

            // 使用默认包名或用户提供的包名
            String targetPackage = StringUtils.isEmpty(packageName) ?
                    scaffoldTemplate.getPackagePath() : packageName;

            // 处理后端代码，替换包名和项目名称
            GitFileUtil.replaceAppNameAndPackage(sourcePath, targetPath, programNameEn, targetPackage, serviceName,
                    scaffoldTemplate.getPackagePath());
            //集成starter组件
            if (CollectionUtils.isNotEmpty(scaffoldComponents)) {
                //处理后端代码，pom集成starter组件
                GitFileUtil.addPomStarterComponent(targetPath, scaffoldComponents);
                //处理后端代码，application.yml集成starter组件
                GitFileUtil.addApplicationConfig(targetPath, scaffoldComponents);
            }

        } else {
            // 后端模板模式：修改后端文件内容和包名
            log.info("使用后端模板模式，将替换项目名称和包名");
            // 使用默认包名或用户提供的包名
            String targetPackage = StringUtils.isEmpty(packageName) ?
                    scaffoldTemplate.getPackagePath() : packageName;
            // 处理后端代码，替换包名和项目名称
            GitFileUtil.replaceAppNameAndPackage(sourcePath, targetPath, programNameEn, targetPackage, serviceName,
                    scaffoldTemplate.getPackagePath());
        }
    }


    /**
     * 遍历源目录，复制文件并收集需要重命名的目录
     */
    private static void copyFilesAndCollectDirectories(Path sourcePath, Path targetPath, String programNameEn,
                                                       Map<Path, Path> directoriesToRename, String scaffoldName) throws IOException {
        // 需要处理的前端配置文件列表
        List<String> frontendConfigFiles = Arrays.asList(
                ".env", ".env.development", ".env.production", ".env.test", "package.json", ".gitignore"
        );

        Files.walk(sourcePath)
                .filter(path -> !GitFileUtil.isGitDirectory(path))
                .forEach(source -> {
                    try {
                        // 处理路径替换
                        Path relativePath = processRelativePath(sourcePath, source, programNameEn, scaffoldName);
                        Path dest = targetPath.resolve(relativePath);

                        if (Files.isDirectory(source)) {
                            handleDirectory(source, dest, programNameEn, directoriesToRename, scaffoldName);
                        } else {
                            handleFile(source, dest, relativePath, frontendConfigFiles, programNameEn, scaffoldName);
                        }
                    } catch (IOException e) {
                        log.error("复制文件失败: {}", e.getMessage());
                        throw new GitException(ExceptionCode.CREATE_MERGE_REQUEST_FAIL, "复制文件失败: " + e.getMessage());
                    }
                });
    }

    /**
     * 处理相对路径，替换项目名称
     */
    private static Path processRelativePath(Path sourcePath, Path source, String programNameEn, String scaffoldName) {
        Path relativePath = sourcePath.relativize(source);
        String pathStr = relativePath.toString();

        // 检查路径中是否包含需要替换的项目名称
        if (pathStr.contains(scaffoldName)) {
            // 创建新的相对路径，替换项目名称
            String newPathStr = pathStr.replace(scaffoldName, programNameEn);
            relativePath = Paths.get(newPathStr);
            log.info("重命名路径: {} -> {}", pathStr, newPathStr);
        }

        return relativePath;
    }

    /**
     * 处理目录，创建目录并收集需要重命名的目录
     */
    private static void handleDirectory(Path source, Path dest, String programNameEn,
                                        Map<Path, Path> directoriesToRename, String scaffoldName) throws IOException {
        // 创建目标目录
        if (!Files.exists(dest)) {
            Files.createDirectories(dest);
        }

        // 如果目录名包含项目名称，记录下来以便后续重命名
        if (source.getFileName().toString().equals(scaffoldName)) {
            Path newDest = dest.getParent().resolve(programNameEn);
            directoriesToRename.put(dest, newDest);
        }
    }

    /**
     * 处理文件，替换内容或直接复制
     */
    private static void handleFile(Path source, Path dest, Path relativePath,
                                   List<String> frontendConfigFiles, String programNameEn,
                                   String scaffoldName) throws IOException {
        // 确保父目录存在
        Files.createDirectories(dest.getParent());

        // 检查是否需要处理的前端配置文件
        String fileName = source.getFileName().toString();
        if (frontendConfigFiles.contains(fileName)) {
            processConfigFile(source, dest, relativePath, programNameEn, scaffoldName);
        } else {
            // 非配置文件，直接复制
            Files.copy(source, dest, StandardCopyOption.REPLACE_EXISTING);
        }
    }

    /**
     * 处理配置文件，替换内容中的项目名称
     */
    private static void processConfigFile(Path source, Path dest, Path relativePath,
                                          String programNameEn, String scaffoldName) throws IOException {
        // 读取文件内容
        String content = new String(Files.readAllBytes(source), StandardCharsets.UTF_8);

        // 替换项目名称
        if (content.contains(scaffoldName)) {
            content = content.replace(scaffoldName, programNameEn);
            // 写入修改后的内容
            Files.write(dest, content.getBytes(StandardCharsets.UTF_8));
            log.info("已替换文件 {} 中的项目名称为: {}", relativePath, programNameEn);
        } else {
            // 无需替换，直接复制
            Files.copy(source, dest, StandardCopyOption.REPLACE_EXISTING);
        }
    }


    // 新增动态获取命名空间ID方法
    private static int getNamespaceId(String apiBase, String token, String namespacePath) throws Exception {
        HttpGet get = new HttpGet(apiBase + "/groups/" + URLEncoder.encode(namespacePath, "UTF-8"));
        get.setHeader(PRIVATE_TOKEN, token);

        try (CloseableHttpResponse response = HttpClients.createDefault().execute(get)) {
            JSONObject json = JSON.parseObject(EntityUtils.toString(response.getEntity()));
            return json.getIntValue("id");
        }
    }


    public static void addUsersToProject(String url, String privateToken, List<String> usernames) {
        URI uri = URI.create(url);
        String path = uri.getPath();
        String domain = uri.getScheme() + "://" + uri.getHost();
        String projectPath = path.substring(1).replace(".git", "");
        try (GitLabApi gitLabApi = new GitLabApi(domain, privateToken)) {
            Project project = gitLabApi.getProjectApi().getProject(projectPath);
            for (String username : usernames) {
                gitLabApi.getUserApi().getOptionalUser(username).ifPresent(e -> {
                    try {
                        gitLabApi.getProjectApi().addMember(project.getId(), e.getId(), GitlabAccessLevelEnum.DEVELOPER.getLevel());
                    } catch (Exception ex) {
                        log.error("添加用户:【{}】异常", username, ex);
                    }
                });
            }
        } catch (Exception e) {
            log.error("批量添加用户到GitLab项目失败", e);
        }
    }

    public static void removeUsersFromProject(String url, String privateToken, List<String> usernames) {
        URI uri = URI.create(url);
        String path = uri.getPath();
        String domain = uri.getScheme() + "://" + uri.getHost();
        String projectPath = path.substring(1).replace(".git", "");
        try (GitLabApi gitLabApi = new GitLabApi(domain, privateToken)) {
            Project project = gitLabApi.getProjectApi().getProject(projectPath);
            for (String username : usernames) {
                gitLabApi.getUserApi().getOptionalUser(username).ifPresent(e -> {
                    try {
                        gitLabApi.getProjectApi().removeMember(project.getId(), e.getId());
                    } catch (Exception ex) {
                        log.error("移除用户:【{}】异常", username, ex);
                    }
                });
            }
        } catch (Exception e) {
            log.error("批量从GitLab项目移除用户失败", e);
        }
    }

    /**
     * 生产用master 开发用develop 测试和预生产用release
     *
     * @param pushGitUrl
     * @param pushPrivateToken
     * @param master
     */
    public static void create4EnvBranch(String pushGitUrl, String pushPrivateToken, String master) {
        log.warn("pushGitUrl is {}", pushGitUrl);
        log.warn("pushPrivateToken is {}", pushPrivateToken);
        log.warn("master is {}", master);
        try {
            URI uri = URI.create(pushGitUrl);
            String domain = uri.getScheme() + "://" + uri.getHost();
            String path = uri.getPath();
            String projectPath = path.substring(1).replace(".git", "");
            log.warn("projectPath is {}", projectPath);
            // 使用正确的参数创建 GitLabApi 实例
            try (GitLabApi gitLabApi = new GitLabApi(domain, pushPrivateToken)) {
                // 获取项目ID或路径
                Project project = gitLabApi.getProjectApi().getProject(projectPath);
                Long projectId = project.getId();
                // 使用项目ID创建分支
                gitLabApi.getRepositoryApi().createBranch(projectId, "develop", master);
                gitLabApi.getRepositoryApi().createBranch(projectId, "release", master);
                log.info("成功为项目 {} 创建2个环境分支", projectPath);
            }
        } catch (Exception e) {
            log.error("创建4个环境分支失败: {}", e.getMessage(), e);
            throw new GitException("创建环境分支失败: " + e.getMessage());
        }
    }

    /**
     * 根据时间范围和项目IDs查询用户提交次数、用户名、项目ID和Git URL
     *
     * @param gitlabDomain GitLab域名
     * @param privateToken GitLab访问令牌
     * @param gitlabIds    项目ID列表
     * @param startTime    开始时间（格式：yyyy-MM-dd HH:mm:ss）
     * @param endTime      结束时间（格式：yyyy-MM-dd HH:mm:ss）
     * @return 用户提交统计列表，按提交次数降序排序
     */
    public static List<GitlabCommitCountDTO> getUserCommitCountsByTimeAndGitlabIds(
            String gitlabDomain, String privateToken, List<String> gitlabIds,
            String startTime, String endTime) {
        // 参数校验
        if (StringUtils.isEmpty(gitlabDomain) || StringUtils.isEmpty(privateToken) || CollectionUtils.isEmpty(gitlabIds)) {
            log.error("参数错误：GitLab域名、访问令牌或项目ID列表不能为空");
            throw new IllegalArgumentException("参数错误：GitLab域名、访问令牌或项目ID列表不能为空");
        }

        // 时间格式校验与转换
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date start = null;
        Date end = null;
        try {
            if (StringUtils.isNotEmpty(startTime)) {
                start = sdf.parse(startTime);
            }
            if (StringUtils.isNotEmpty(endTime)) {
                end = sdf.parse(endTime);
            }
        } catch (ParseException e) {
            log.error("时间格式错误：{}", e.getMessage());
            throw new IllegalArgumentException("时间格式错误，请使用yyyy-MM-dd HH:mm:ss格式");
        }

        // 存储提交统计信息
        List<CommitStatistics> commitStatisticsList = Collections.synchronizedList(new ArrayList<>());
        // 存储项目ID与Git URL的映射关系
        Map<String, String> projectGitUrlMap = Collections.synchronizedMap(new HashMap<>());

        // 创建线程池，线程数可根据实际需求调整
        int threadPoolSize = Math.min(gitlabIds.size(), 10); // 最多10个线程
        ExecutorService executorService = Executors.newFixedThreadPool(threadPoolSize);

        try {
            // 并行处理每个项目ID
            Date finalStart = start;
            Date finalEnd = end;
            List<CommitStatistics> finalCommitStatisticsList = commitStatisticsList;
            List<CommitStatistics> finalCommitStatisticsList1 = commitStatisticsList;
            List<CompletableFuture<Void>> futures = gitlabIds.stream()
                    .map(projectId -> CompletableFuture.runAsync(() -> {
                        try {
                            // 首先获取项目信息，包括git url
                            String projectApiUrl = gitlabDomain + "/api/v4/projects/" + projectId;
                            HttpGet projectGet = new HttpGet(projectApiUrl);
                            projectGet.setHeader(PRIVATE_TOKEN, privateToken);
                            projectGet.setHeader("Accept", "application/json");

                            try (CloseableHttpClient projectHttpClient = HttpClients.createDefault();
                                 CloseableHttpResponse projectResponse = projectHttpClient.execute(projectGet)) {
                                int projectStatusCode = projectResponse.getStatusLine().getStatusCode();
                                if (projectStatusCode == HttpStatus.SC_OK) {
                                    String projectResponseBody = EntityUtils.toString(projectResponse.getEntity());
                                    JSONObject project = JSON.parseObject(projectResponseBody);
                                    String gitUrl = project.getString("http_url_to_repo");
                                    projectGitUrlMap.put(projectId, gitUrl);
                                    log.info("获取项目 {} 的Git URL: {}", projectId, gitUrl);
                                } else {
                                    String projectErrorBody = EntityUtils.toString(projectResponse.getEntity());
                                    log.error("获取项目 {} 信息失败，状态码: {}, 响应: {}", projectId, projectStatusCode, projectErrorBody);
                                }
                            } catch (IOException e) {
                                log.error("获取项目 {} 信息IO异常: {}", projectId, e.getMessage());
                            }

                            // 构造提交记录API URL
                            StringBuilder apiUrlBuilder = new StringBuilder();
                            apiUrlBuilder.append(gitlabDomain)
                                    .append("/api/v4/projects/").append(projectId)
                                    .append("/repository/commits?per_page=1000");

                            // 添加时间参数
                            if (finalStart != null) {
                                apiUrlBuilder.append("&since=")
                                        .append(URLEncoder.encode(sdf.format(finalStart), StandardCharsets.UTF_8));
                            }
                            if (finalEnd != null) {
                                apiUrlBuilder.append("&until=")
                                        .append(URLEncoder.encode(sdf.format(finalEnd), StandardCharsets.UTF_8));
                            }

                            String apiUrl = apiUrlBuilder.toString();
                            log.info("查询项目 {} 的提交记录API: {}", projectId, apiUrl);

                            // 创建HTTP请求
                            HttpGet httpGet = new HttpGet(apiUrl);
                            httpGet.setHeader(PRIVATE_TOKEN, privateToken);
                            httpGet.setHeader("Accept", "application/json");

                            // 执行请求
                            try (CloseableHttpClient httpClient = HttpClients.createDefault();
                                 CloseableHttpResponse response = httpClient.execute(httpGet)) {

                                int statusCode = response.getStatusLine().getStatusCode();
                                if (statusCode != HttpStatus.SC_OK) {
                                    String errorBody = EntityUtils.toString(response.getEntity());
                                    log.error("查询项目 {} 提交记录失败，状态码: {}, 响应: {}", projectId, statusCode, errorBody);
                                    return;
                                }

                                // 解析响应
                                String responseBody = EntityUtils.toString(response.getEntity());
                                JSONArray commits = JSON.parseArray(responseBody);

                                // 统计提交次数
                                for (Object commitObj : commits) {
                                    JSONObject commit = (JSONObject) commitObj;
                                    String username = commit.getString("author_name");
                                    if (StringUtils.isNotEmpty(username)) {
                                        // 查找或创建提交统计对象
                                        synchronized (finalCommitStatisticsList) {
                                            Optional<CommitStatistics> existingStats = finalCommitStatisticsList1.stream()
                                                    .filter(stat -> stat.getUsername().equals(username) && stat.getProjectId().equals(projectId))
                                                    .findFirst();

                                            if (existingStats.isPresent()) {
                                                existingStats.get().incrementCommitCount();
                                            } else {
                                                finalCommitStatisticsList1.add(new CommitStatistics(username, projectId, 1));
                                            }
                                        }
                                    }

                                }
                            } catch (IOException e) {
                                log.error("查询项目 {} 提交记录IO异常: {}", projectId, e.getMessage());
                            }
                        } catch (Exception e) {
                            log.error("处理项目 {} 提交记录异常: {}", projectId, e.getMessage());
                        }
                    }, executorService))
                    .toList();

            // 等待所有任务完成
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

        } finally {
            // 关闭线程池
            executorService.shutdown();
            try {
                if (!executorService.awaitTermination(1, TimeUnit.MINUTES)) {
                    executorService.shutdownNow();
                }
            } catch (InterruptedException e) {
                executorService.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }

        // 去重合并相同用户和项目的提交记录
        Map<String, CommitStatistics> uniqueStatsMap = new HashMap<>();
        for (CommitStatistics stats : commitStatisticsList) {
            String key = stats.getUsername() + ":" + stats.getProjectId();
            if (uniqueStatsMap.containsKey(key)) {
                uniqueStatsMap.get(key).setCommitCount(
                        uniqueStatsMap.get(key).getCommitCount() + stats.getCommitCount());
            } else {
                uniqueStatsMap.put(key, new CommitStatistics(
                        stats.getUsername(), stats.getProjectId(), stats.getCommitCount()));
            }
        }
        commitStatisticsList = new ArrayList<>(uniqueStatsMap.values());

        // 转换为DTO并按提交次数降序排序
        List<GitlabCommitCountDTO> commitCountDTOs = commitStatisticsList.stream()
                .map(stat -> {
                    String gitUrl = projectGitUrlMap.getOrDefault(stat.getProjectId(), "");
                    return new GitlabCommitCountDTO(stat.getUsername(), stat.getCommitCount(), stat.getProjectId(), gitUrl);
                })
                .sorted((d1, d2) -> Integer.compare(d2.getCommitCount(), d1.getCommitCount()))
                .collect(Collectors.toList());
        return commitCountDTOs;
    }

}


