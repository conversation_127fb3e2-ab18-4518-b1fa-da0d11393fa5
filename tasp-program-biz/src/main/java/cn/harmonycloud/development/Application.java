package cn.harmonycloud.development;

import cn.harmonycloud.common.datasource.EnableMasterSlaveDataSource;
import cn.harmonycloud.openapi.EnableOpenApi;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.client.loadbalancer.LoadBalanced;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import org.springframework.web.client.RestTemplate;

/**
 * @usage Harmony-cloud默认代码生成
 */
@EnableMasterSlaveDataSource
@SpringBootApplication(scanBasePackages = "cn.harmonycloud")
@MapperScan({"cn.harmonycloud.**.outbound.db.**", "cn.harmonycloud.development.outbound.trinasolar"})
@EnableOpenApi
@EnableFeignClients(basePackages = {"cn.harmonycloud"})
@EnableTransactionManagement
@EnableAspectJAutoProxy
@EnableDiscoveryClient
public class Application {

    public static void main(String[] args) {
        // 禁用 Nacos 内部日志配置，避免 CONFIG_LOG_FILE 冲突
        System.setProperty("nacos.logging.default.config.enabled", "false");
        System.setProperty("nacos.logging.config", "");
        System.setProperty("com.alibaba.nacos.naming.log.level", "WARN");
        System.setProperty("com.alibaba.nacos.config.log.level", "WARN");

        SpringApplication.run(Application.class, args);
    }

    @Bean
    @LoadBalanced
    public RestTemplate restTemplate() {
        return new RestTemplate();
    }
}


