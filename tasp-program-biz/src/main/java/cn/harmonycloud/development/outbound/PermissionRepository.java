package cn.harmonycloud.development.outbound;

import cn.harmonycloud.development.outbound.api.dto.role.RoleBindingDto;
import cn.harmonycloud.development.outbound.api.dto.role.RoleInfoDto;
import cn.harmonycloud.pmp.model.dto.UserOrganizationDto;
import cn.harmonycloud.pmp.model.entity.ResourceInstance;
import cn.harmonycloud.pmp.model.entity.RoleBase;
import cn.harmonycloud.pmp.model.vo.RoleVo;
import cn.harmonycloud.pmp.model.vo.UserRoleVo;

import java.util.List;
import java.util.Map;

public interface PermissionRepository {

    /**
     * 查询用户在资源下的角色列表
     *
     * @param resourceTypeCode 资源类型
     * @param instanceId       资源id
     * @param userIds           用户id
     * @return
     */
    List<UserRoleVo> getRolesByInstance(String resourceTypeCode, Long instanceId, List<Long> userIds);

    /**
     * 注册资源实例
     *
     * @param resourceTypeCode 资源类型code
     * @param instanceId       资源实例id
     * @param instanceName     资源实例名称
     * @param parentId         父级资源实例id
     * @return
     */
    Boolean resourceInstances(String resourceTypeCode, Long instanceId, String instanceName, String parentCode, String parentId);

    /**
     * 删除资源实例
     *
     * @return
     */
    void deleteResourceInstances(String resourceTypeCode, Long resourceInstanceId);
    /**
     * 更新资源实例
     *
     * @param resourceTypeCode 资源类型code
     * @param instanceId       资源实例id
     * @param instanceName     资源实例名称
     * @return
     */
    Boolean updateResourceInstances(String resourceTypeCode, Long instanceId, String instanceName);
    /**
     * 根据资源类型查询角色列表
     *
     * @param resourceTypeCode
     * @return
     */
    List<RoleInfoDto> roleList(String resourceTypeCode);
    /**
     * 查看单个角色详情
     *
     * @param roleId 角色id
     * @return
     */
    RoleVo roleDetails(Long roleId);

    /**
     * 添加成员
     * @param resourceUser
     * @return
     */
    Boolean resourceInstancesUser(UserOrganizationDto resourceUser);

    /**
     * 更新成员角色
     *
     * @param resourceTypeCode
     * @param resourceInstanceId
     * @param userId
     * @param roleIds
     * @return
     */
    Boolean resourceUpdateRole(String resourceTypeCode, Long resourceInstanceId, Long userId, List<Long> roleIds);

    /**
     * 资源列表
     * @param parentCode 父级资源code
     * @param parentId 父级资源id
     * @param resourceTypeCode 资源code
     * @return
     */
    List<ResourceInstance> resourceList(String parentCode, String parentId, String resourceTypeCode);

    /**
     * 资源列表
     * @param resourceTypeCode 资源code
     * @return
     */
    List<ResourceInstance> resourceList(String resourceTypeCode);

    /**
     * 查询amp的gitlab绑定角色信息
     *
     * @return
     */
    RoleBindingDto getGitlabGroupRoleBindingInfo(String resourceTypeCode, String roleId);

    /**
     * 查询amp的gitlab绑定角色信息
     *
     * @return
     */
    RoleBindingDto getGitlabRoleBindingInfo(String resourceTypeCode, String roleId);

    /**
     * 查询流水线绑定角色信息
     *
     * @return
     */
    RoleBindingDto getPipelineRoleBindingInfo(String resourceTypeCode, String roleId);

    /**
     * 查询绑定角色列表
     *
     * @param resourceTypeCode 资源code
     * @param targetResourceCode
     * @return
     */
    List<RoleBindingDto> listRoleBindingInfo(String resourceTypeCode, String targetResourceCode);

    /**
     * 查询指定资源下的的角色列表
     *
     * @return
     */
    List<UserRoleVo> getRoleByUser(String resourceTypeCode, String resourceInstanceId, Long userId);

    /**
     *
     * 查询当前用户在多个资源下的权限
     */
    Map<Long, Map<String, Boolean>> mapPermission(String resourceTypeCode, List<Long> resourceInstanceIds);

    /**
     * 查询用户在多个资源下的角色
     *
     * @param userId 用户id
     * @param resourceTypeCode 资源code
     * @param resourceInstanceIds 资源id
     */
    Map<Long, List<RoleBase>> listRoleByUserId(Long userId, String resourceTypeCode, List<Long>resourceInstanceIds);

    /**
     * 批量修改用户在多个资源下的角色
     *
     * @param resourceTypeCode
     * @param resourceInstanceIds
     * @param userId
     * @param roleId
     */
    void modifyMemberBatch(String resourceTypeCode, List<Long> resourceInstanceIds, Long userId, Long roleId);

    void batchDeleteResourceInstance(String resourceTypeCode, List<Long> resourceInstanceIds);

    /**
     * 校验用户是否有资源权限
     *
     * @return
     */
    boolean checkResource(String resourceTypeCode, Long resourceInstanceId, String parentTypeCode, Long parentId);
}