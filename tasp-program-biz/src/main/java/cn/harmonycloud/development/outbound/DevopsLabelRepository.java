package cn.harmonycloud.development.outbound;

import cn.harmonycloud.development.pojo.entity.DevopsLabel;
import cn.harmonycloud.development.pojo.vo.label.DevopsLabelCreateDto;
import cn.harmonycloud.mybatis.base.BaseRepository;
import cn.harmonycloud.pmp.model.entity.Tag;

import java.util.List;
import java.util.Map;

public interface DevopsLabelRepository extends BaseRepository<DevopsLabel> {

    /**
     * 批量添加关联关系
     *
     * @param request 关联关系集合列表（同一个instanceId）
     */
    void removeAndSave(DevopsLabelCreateDto request);

    /**
     * 批量保存关联关系(删除所有原有关系)
     *
     * @param classificationCode 分类code
     * @param instanceId 实例id
     * @param labelsIds 标签id列表
     */
    void removeAndSave(String classificationCode, Long instanceId, List<Long> labelsIds);

    /**
     * 删除标签关联关系
     *
     * @param classificationCode 分类id
     * @param instanceId 实例id
     * @param labelId 标签id
     */
    boolean removeByParams(String classificationCode, Long instanceId, Long labelId);

    /**
     * 根据参数查询实例id
     *
     * @param classificationCode 分类编码
     * @param labelIds 标签id
     * @return
     */
    List<Long> listInstanceId(String classificationCode, List<Long> labelIds);


    /**
     * 根据分类编码和实例id查询标签列表的map
     *
     * @param classificationCode 分类编码
     * @param instanceIds 实例ids
     */
    Map<Long, List<Tag>> mapLabelByInstanceId(String classificationCode, List<Long> instanceIds);

    /**
     * 根据分类编码和实例id查询标签id列表
     *
     * @param classificationCode 分类编码
     * @param instanceId 实例id
     */
    List<Long> listLabelByInstanceId(String classificationCode, Long instanceId);
}
