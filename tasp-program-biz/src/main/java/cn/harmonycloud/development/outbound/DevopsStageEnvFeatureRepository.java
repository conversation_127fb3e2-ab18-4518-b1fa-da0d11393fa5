package cn.harmonycloud.development.outbound;

import cn.harmonycloud.development.pojo.entity.DevopsStageEnvFeature;
import cn.harmonycloud.mybatis.base.BaseRepository;

import java.util.List;

public interface DevopsStageEnvFeatureRepository extends BaseRepository<DevopsStageEnvFeature>{

    /**
     * 根据阶段环境id查询特性id列表
     * @param stageEnvId
     * @return 特性id列表
     */
    List<Long> selectFeatureList(Long stageEnvId, Long versionId);

    /**
     * 移除特性列表
     * @param stageEnvId
     * @param featureIds
     */
    void deleteByParam(Long stageEnvId, Long versionId, List<Long> featureIds);

    /**
     * 添加特性列表
     * @param stageEnvId
     * @param featureIds
     */
    void add(Long stageEnvId, Long versionId, List<Long> featureIds);
}
