package cn.harmonycloud.development.outbound;

import cn.harmonycloud.development.pojo.vo.scm.MyMergeRequestVO;
import cn.harmonycloud.development.pojo.vo.testenv.MergeRequestSettingVO;
import cn.harmonycloud.development.outbound.api.dto.scm.*;
import cn.harmonycloud.pojo.coderepo.MergeStatisticInfoResponse;

import java.util.List;

public interface CodeMergeRepository {

    /********************  分支合并器  ****************/

    /**
     * 创建合并合并器
     *
     * @param request
     * @return
     */
    MergeTaskResponse mergeTask(MergeTaskRequest request);

    /**
     * 继续执行分支合并器
     *
     * @param mergeTaskBranchId 分支合并器的分支id
     */
    void proceedMergeTask(Long mergeTaskBranchId);

    /**
     * 取消分支合并器
     *
     * @param mergeTaskId 分支合并器实例id
     */
    void mergeRequestCancel(Long mergeTaskId);

    /**
     * 查询分支合并器详情
     *
     * @param mergeGroupId 分支合并器id
     * @return
     */
    MergeTaskDto getMergeTask(Long mergeGroupId);

    /**
     * 查询分支合并器详情
     *
     * @param taskId 分支合并器实例id
     * @return
     */
    MergeTaskDto getMergeTaskByTaskId(Long taskId);

    /********************  分支请求  ****************/
    /**
     * 发起合并请求
     *
     * @param gitlabId
     * @param requestDTO
     * @return
     */
    Integer submitRequestMerge(Integer gitlabId, MergeRequestDTO requestDTO);

    /**
     * 合并请求详情
     *
     * @param gitlabId 代码仓库id
     * @param mergeIid 合并请求iid
     * @return
     */
    Object requestMergeDetails(Integer gitlabId, Integer mergeIid);

    /**
     * 统计merge数
     *
     * @param gitlabId 分组id
     * @param from
     * @param from
     */
    Integer mergeCount(Integer gitlabId, String from, String to);

    /**
     * 查询合并请求配置
     *
     * @param gitlabId
     * @return
     */
    MergeRequestSettingVO mergeSetting(Integer gitlabId);

    /**
     * 取消合并请求
     *
     * @param gitlabId
     * @param mergeIid
     */
    void merClose(Integer gitlabId, Integer mergeIid);

    /**
     * 子系统工作台-评审列表
     *
     * @param gitlabId
     * @param type     0-我创建的，1-我负责的
     * @return
     */
    List<MyMergeRequestVO> myMergeRequest(int gitlabId, Integer type);

    /**
     * 统计代码库下的代码提交数
     *
     * @param gitlabIds
     * @return
     */
    List<MergeStatisticInfoResponse> mergeStatistic(List<Integer> gitlabIds);
}
