package cn.harmonycloud.development.outbound;

import cn.harmonycloud.development.outbound.api.dto.repository.DevopsRepository;
import cn.harmonycloud.development.outbound.api.dto.repository.PromotionStrategyDTO;
import cn.harmonycloud.development.pojo.dto.repository.SystemRepoQuery;
import cn.harmonycloud.development.pojo.entity.DevopsSystem;
import cn.harmonycloud.development.pojo.vo.repository.CreateRepositoryRequest;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

public interface SystemRepoRepository {

    void create(CreateRepositoryRequest request, DevopsSystem devopsSystem);

    List<DevopsRepository> listByParams(SystemRepoQuery query);

    void delete(Long id);

    /**
     * 查询系统晋级策略
     *
     * @param systemId
     * @param format
     * @return
     */
    List<PromotionStrategyDTO> listPromotionStrategy(Long systemId, String format);

    /**
     *
     * @param ids
     * @return
     */
    List<DevopsRepository> listByIds(List<Long> ids);

    /**
     * 取消制品库绑定关系
     *
     * @param systemId
     */
    void unBind(Long systemId);
}
