package cn.harmonycloud.development.outbound.util;

import javax.crypto.Cipher;
import javax.crypto.SecretKeyFactory;
import javax.crypto.spec.DESKeySpec;
import javax.crypto.spec.IvParameterSpec;
import java.security.Key;
import java.util.Base64;

/**
 * 行内密码加密工具类
 * <AUTHOR>
 * @Date 2022/8/8
 */
public class DesUtil {
    /**
     * 偏移变量，固定占8位字节
     */
    private static final String IV_PARAMETER = "12345678";

    /**
     * 秘钥算法
     */
    private static final String ALGORITHM = "DES";

    /**
     * 加密/解密算法-工作默认
     */
    private static final String CIPHER_ALGORITHM = "DES/CBC/PKCS5Padding";

    /**
     * 默认编码
     */
    private static final String CHARSET = "utf-8";

    private static final String PASS = "djaiskdkais";

    /**
     * 生成key
     * @param password
     * @return
     * @throws Exception
     */
    private static Key generateKey(String password) throws Exception {
        DESKeySpec dks = new DESKeySpec(password.getBytes(CHARSET));
        SecretKeyFactory keyFactory = SecretKeyFactory.getInstance(ALGORITHM);
        return keyFactory.generateSecret(dks);
    }

    /**
     * DES加密字符串
     * @param data
     * @return
     */
    public static String encrypt(String data) {
        if (data == null) {
            return null;
        }
        try {
            Key secretKey = generateKey(PASS);
            Cipher cipher = Cipher.getInstance(CIPHER_ALGORITHM);
            IvParameterSpec iv = new IvParameterSpec(IV_PARAMETER.getBytes(CHARSET));
            cipher.init(Cipher.ENCRYPT_MODE, secretKey, iv);
            byte[] bytes = cipher.doFinal(data.getBytes(CHARSET));
            return new String(Base64.getEncoder().encode(bytes));
        } catch (Exception e) {
            e.printStackTrace();
            return data;
        }
    }

    /**
     * DES解密字符串
     * @param data
     * @return
     */
    public static String decrypt(String data) {
        if (data == null) {
            return null;
        }
        try {
            Key secretKey = generateKey(PASS);
            Cipher cipher = Cipher.getInstance(CIPHER_ALGORITHM);
            IvParameterSpec iv = new IvParameterSpec(IV_PARAMETER.getBytes(CHARSET));
            cipher.init(Cipher.DECRYPT_MODE, secretKey, iv);
            return new String(cipher.doFinal(Base64.getDecoder().decode(data.getBytes(CHARSET))), CHARSET);
        } catch (Exception e) {
            e.printStackTrace();
            return data;
        }
    }
}
