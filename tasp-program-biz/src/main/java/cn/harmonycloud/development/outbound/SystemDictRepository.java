package cn.harmonycloud.development.outbound;

import cn.harmonycloud.development.pojo.entity.SystemDict;
import cn.harmonycloud.mybatis.base.BaseRepository;

import java.util.List;
import java.util.Map;

public interface SystemDictRepository extends BaseRepository<SystemDict> {

    /**
     * 查询所有字典
     *
     * @return
     */
    List<SystemDict> listAll();

    /**
     * 按subject分组查询字典
     *
     * @param useCache true表示使用缓存，false-不是缓存
     * @return
     */
    Map<String, List<SystemDict>> getAllDict(boolean useCache);

    /**
     * 根据指定分类查询字典
     *
     * @param subject
     * @return
     */
    List<SystemDict> getByParams(String subject);

    /**
     * 更据id查询数据
     *
     * @param id
     * @return
     */
    SystemDict getCacheById(Long id);
}
