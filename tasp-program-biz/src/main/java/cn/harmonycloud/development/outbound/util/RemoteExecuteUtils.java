package cn.harmonycloud.development.outbound.util;

import cn.harmonycloud.common.core.base.BaseResult;
import cn.harmonycloud.common.core.exception.UnauthorizedException;
import cn.harmonycloud.constants.ApiConstance;
import cn.harmonycloud.enums.ExceptionCode;
import cn.harmonycloud.exception.SystemException;
import cn.harmonycloud.util.LogUtils;
import lombok.extern.slf4j.Slf4j;

/**
 * @Description
 * <AUTHOR>
 * @Date 2022/7/11 7:03 下午
 **/
@Slf4j
public class RemoteExecuteUtils {

    public static <T> BaseResult<T> execute(Execute<T> execute, ExceptionCode exceptionCode){
        BaseResult<T> result;
        try {
            result = execute.remote();
        }catch (Exception e){
            log.error(LogUtils.throwableExceptionString(e));
            throw new SystemException(exceptionCode, e.getMessage());
        }
        if(result.getCode() == ApiConstance.OAUTH){
            throw new UnauthorizedException(result.getMsg());
        }
        if (result.getCode() != ApiConstance.SUCCESS){
            throw new SystemException(exceptionCode, result.getMsg());
        }
        return result;
    }

    public interface Execute<T>{
        BaseResult<T> remote();
    }

}
