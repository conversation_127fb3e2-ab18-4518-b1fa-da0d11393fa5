package cn.harmonycloud.development.outbound;

import cn.harmonycloud.development.pojo.entity.VersionComponents;
import cn.harmonycloud.mybatis.base.BaseRepository;

import javax.annotation.Nullable;
import java.util.List;

public interface VersionComponentsRepository extends BaseRepository<VersionComponents> {

    /**
     * 获取版本关联特性
     *
     * @param versionId
     */
    List<Long> listFeatureIdByParams(Long versionId);

    /**
     * 根据参数查询结果
     *
     * @param versionId
     * @param component
     * @param componentKey
     * @return
     */
    List<VersionComponents> listByParams(Long versionId, @Nullable String component, @Nullable String componentKey);

    /**
     * 根据参数查询结果
     *
     * @param versionIds
     * @param component
     * @param componentKey
     * @return
     */
    List<VersionComponents> listByParams(List<Long> versionIds, @Nullable String component, @Nullable String componentKey);

    /**
     * 删除数据
     *
     * @param versionId 版本Id
     * @param component 组件名称
     * @param keys  组件id
     */
    boolean removeByParams(Long versionId, String component, @Nullable List<String> keys);

    boolean removeBySubordination(Long versionId , String component , List<Long> subordination);

    VersionComponents getSubordination(Long versionId, String component,Long subordination);

    /**
     * 删除版本下的组件信息
     *
     * @param subVersionIds
     * @return
     */
    boolean removeByVersionIds(List<Long> subVersionIds);

    List<VersionComponents> getByComponentKey(Long versionId , String component , List<String> keys);
}
