package cn.harmonycloud.development.outbound;

import cn.harmonycloud.development.outbound.api.dto.project.ProjectManagementDto;

import java.util.List;
import java.util.Map;

/**
 * 项目管理仓储
 *
 */
public interface ProjectRepository {

    /**
     * 项目列表
     *
     * @param ids
     * @return
     */
    List<ProjectManagementDto> getBaseProjects(List<Long> ids);

    /**
     * 更据项目id列表查询项目列表
     *
     * @param ids
     * @return
     */
    List<ProjectManagementDto> getAllProjects(List<Long> ids);


    /**
     * 根据项目id列表查询项目集合
     *
     * @param ids
     * @return
     */
    Map<Long, ProjectManagementDto> mapAllProjects(List<Long> ids);

    /**
     * 根据项目id列表查询项目集合
     *
     * @param ids
     * @return
     */
    Map<Long, ProjectManagementDto> mapBaseProjects(List<Long> ids);

    /**
     * 根据项目id插叙项目详情
     *
     * @param projectId
     * @return
     */
    ProjectManagementDto getById(Long projectId);

    /**
     * 项目列表
     *
     * @return
     */
    List<ProjectManagementDto> getByUser();
    
}
