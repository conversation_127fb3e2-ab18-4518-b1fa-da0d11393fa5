package cn.harmonycloud.development.outbound;

import cn.harmonycloud.development.outbound.api.dto.pipeline.DeployHostDTO;
import cn.harmonycloud.development.outbound.api.dto.pipeline.DoDeployEnv;
import cn.harmonycloud.development.pojo.dto.operation.SystemEnv;

import java.util.List;
import java.util.Map;

public interface DeployEnvRepository {

    Map<Long, DoDeployEnv> envMap(List<Long> envIds);

    Map<Long, DoDeployEnv> envMap();

    DoDeployEnv getById(Long envId);

    List<SystemEnv> createSystemEnv(List<SystemEnv> systemEnvList);

    Map<Integer, DeployHostDTO> mapDeployHostByIds(List<Integer> hostIds);

    List<DoDeployEnv> listBySystem(Long systemId);
}
