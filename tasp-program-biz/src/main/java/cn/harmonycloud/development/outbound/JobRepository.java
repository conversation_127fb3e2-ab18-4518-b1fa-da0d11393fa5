package cn.harmonycloud.development.outbound;

import cn.harmonycloud.development.pojo.entity.DevopsSubSystem;
import cn.harmonycloud.development.pojo.entity.DevopsSystem;
import cn.harmonycloud.development.outbound.api.dto.pipeline.*;
import cn.harmonycloud.pmp.model.entity.User;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import cn.harmonycloud.development.outbound.api.dto.pipeline.JenkinsFileStartParameter;

import java.util.List;
import java.util.Map;

/**
 * 流水线job
 */
public interface JobRepository {

    /**
     * 关联流水线 将子系统和流水线job关联
     *
     * @param subsystemId
     * @param userMap
     */
    void bindingSubsystem(Long systemId, Long subsystemId, Map<Long, List<Long>> userMap);

    /**
     * 关联流水线 将子系统和流水线job关联
     *
     * @param systemId
     * @param subsystemId
     * @param jobIds
     */
    void bindingSubsystem(Long systemId, Long subsystemId, List<Long> jobIds);

    /**
     * job列表
     *
     * @param jobIds id集合
     * @return
     */
    List<JobDto> listJob(List<Long> jobIds, Boolean resourceFlag, String draftStatus);

    /**
     * job最近一次构建结果
     *
     * @param jobId
     * @return
     */
    BuildDetailDto getRecentBuildByJobId(Long jobId);

    /**
     * 指定buildId的构建结果
     *
     * @param jobId
     * @param buildId
     * @return
     */
    BuildDetailDto getRecentBuildByBuildId(Long jobId, Long buildId);

    /**
     * 执行流水线
     *
     * @param startParam 启动参数
     * @param jobId jobId
     * @param currenUser 当前用户
     * @return
     */
    Long buildJob(List<JenkinsFileStartParameter> startParam, Long jobId, User currenUser, String runDescribe);

    /**
     * 新增一条流水线
     *
     * @param subSystem 子系统信息
     * @param system 系统信息
     * @param start 启动参数
     * @param env 环境变量
     * @param templateId 模版id
     * @return
     */
    Long createJob(DevopsSubSystem subSystem, DevopsSystem system, List<StartParamDto> start, List<EnvParamDto> env, Long templateId);

    /**
     * job分页列表
     *
     * @param page
     * @param pageSize
     * @param pipelinePageDto
     * @return
     */
    Page<JobDto> listByPage(int page, int pageSize, PipelinePageDto pipelinePageDto);

    /**
     * 统计构建次数
     *
     * @param ids
     * @param startTime
     * @param endTime
     * @return
     */
    Integer countBuild(List<Long> ids, String startTime, String endTime);

    /**
     * 创建一个元数据
     *
     * @param jobId
     * @param metadata
     * @return
     */
    Boolean createMetadata(Long jobId, Map<String, Object> metadata);

    /**
     * 停止构建
     * @param jobId
     */
    void stopBuild(Long jobId);

    /**
     * 获取启动变量
     *
     * @param jobId
     * @return
     */
    List<JenkinsFileStartParameter> getRunStartParams(Long jobId);

    /**
     * 获取启动变量
     *
     * @param jobId
     * @return
     */
    List<JenkinsFileStartParameter> getRunStartParams(Long jobId, Boolean lastFlag);

    /**
     * 获取启动变量
     *
     * @param jobIds
     * @return
     */
    void deleteBing(List<Long> jobIds);

    /**
     * 流水线重试
     *
     * @param buildId
     * @param runDescribe
     * @return
     */
    Long replayJob(Long buildId, String runDescribe);
}
