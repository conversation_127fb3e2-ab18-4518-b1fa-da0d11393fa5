package cn.harmonycloud.development.outbound;

import cn.harmonycloud.development.pojo.entity.MergeTask;
import cn.harmonycloud.mybatis.base.BaseRepository;

public interface MergeTaskRepository extends BaseRepository<MergeTask> {

    /**
     * 根据参数查询分支合并起id
     *
     * @param subsystemId 子系统id
     * @param envCode  子系统分组
     * @return
     */
    Long getTaskIdByParams(Long subsystemId, String envCode);
}
