package cn.harmonycloud.development.outbound;

import cn.harmonycloud.development.outbound.api.dto.scm.*;
import cn.harmonycloud.development.pojo.vo.scm.GetConfigResponse;

import java.util.List;

public interface CodeProjectRepository {



    /**
     * 查询代码仓库详情
     *
     * @param gitlabId
     * @return
     */
    GitProjectDto getProject(Integer gitlabId);

    /**
     * 创建代码仓库
     *
     * @param request
     * @return
     */
    ProjectResponse createProject(ProjectRequest request);

    /**
     * 更新代码仓库
     *
     * @param request
     */
    void updateProject(UpdateProjectRequest request);

    /**
     * 注册代码仓库
     *
     * @param groupId  分组id
     * @param gitlabId 代码仓库id
     */
    void registry(Integer groupId, Integer gitlabId);

    /**
     * 代码仓库列表
     *
     * @param groupId 分组id
     * @return
     */
    List<GitProjectDto> projectList(Integer groupId);

    /**
     * 根据参数查询代码仓库
     *
     * @param groupId 分组id
     * @param path 仓库英文名
     * @return
     */
    GitProjectDto getProject(Integer groupId,  String path);

    /**
     * 添加项目成员
     *
     * @param projectId 分组id
     * @param members
     */
    void addProjectMember(Integer projectId, List<ProjectMemberDto> members);

    /**
     * 删除项目成员
     *
     * @param gitlabId 代码仓库id
     * @param username 用户名
     */
    void deleteProjectMember(Integer gitlabId, String username);

    /**
     * 添加多个项目成员
     *
     * @param userId 分组id
     * @param projectIds
     */
    void updateBatchProjectMember(Long userId, List<Integer> projectIds, Long roleId);


    /**
     * 获取指定分支最近一次扫描详情
     *
     * @param gitlabId 代码仓库id
     * @param branchName 分支名称
     * @return
     */
    ScanIssueWithHistoryDetailVO scanIssues(Integer gitlabId, String branchName);

    /**
     * 初始化代码扫描
     *
     * @param gitlabId
     * @param technology
     */
    void initCodeScan(Integer gitlabId, String technology);

    List<String> envList(Long configId);

    GetConfigResponse getConfig(Long configId);
}
