package cn.harmonycloud.development.outbound;

import cn.harmonycloud.development.pojo.entity.SystemProject;
import cn.harmonycloud.mybatis.base.BaseRepository;

import javax.annotation.Nullable;
import java.util.List;

public interface SystemProjectRepository extends BaseRepository<SystemProject> {

    /**
     * 保存系统和项目的关联关系
     *
     * @param systemId 系统id
     * @param projectId 项目id
     *
     * @desc 删除原有的系统和项目的关联关系，再保存新的关联关系
     */
    void save(Long systemId, List<Long> projectId);

    /**
     * 根据参数查询项目列表
     *
     * @param systemId 系统id
     * @param projectIds 项目ids 不为空时生效
     *
     * @return 项目id列表
     */
    List<Long> listProjectByParams(Long systemId, @Nullable List<Long> projectIds);


    /**
     * 根据参数查询系统id列表
     *
     * @param projectIds 项目ids 不为空时生效
     *
     * @return 系统id列表
     */
    List<Long> listSystemByParams(List<Long> projectIds);

}
