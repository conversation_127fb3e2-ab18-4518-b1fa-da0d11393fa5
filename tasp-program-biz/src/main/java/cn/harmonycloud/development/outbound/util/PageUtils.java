package cn.harmonycloud.development.outbound.util;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description 分页工具
 * <AUTHOR>
 * @Date 2022/6/26 6:53 下午
 **/
public class PageUtils {

    /**
     * Page的参数化类型转化
     * @param source 通过mybatis-plus查询的分页结果
     * @param consumer 参数化类型转化方法
     * @param <T> 源类型
     * @param <E> 目标类型
     * @return
     */
    public static <T, E> Page<T> exchangeRecord(Page<E> source, PageRecordExchange<T, E> consumer) {
        List<T> records = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(source.getRecords())) {
            records = source.getRecords().stream().map(record -> consumer.exchange(record)).collect(Collectors.toList());
        }
        return exchangeRecordData(source, records);
    }

    public static <T, E> Page<T> exchangeRecordData(Page<E> source, List<T> records) {
        Page<T> result = new Page<>(source.getCurrent(), source.getSize(), source.getTotal());
        return result.setRecords(records);
    }

    public interface PageRecordExchange<T, E> {
        T exchange(E source);
    }
}
