package cn.harmonycloud.development.outbound;

import cn.harmonycloud.development.pojo.entity.SubSystemPipeline;
import cn.harmonycloud.development.pojo.vo.pipeline.PipelineBindingRequest;
import cn.harmonycloud.mybatis.base.BaseRepository;
import cn.harmonycloud.pmp.model.entity.User;
import java.util.List;
import java.util.Map;

/**
 * @Description 子系统流水线关联仓储
 * <AUTHOR>
 * @Date 2023/4/10 4:59 下午
 **/
public interface SubsystemPipelineRepository extends BaseRepository<SubSystemPipeline> {

    /**
     * 根据子系统id查询列表
     *
     * @param subsystemId
     * @return
     */
    List<SubSystemPipeline> listByParams(Long subsystemId);

    List<Long> getJobIds(Long subsystemId);

    void saveJobs(Long subsystemId, List<Long> jobIds, User currentUser);

    Long getSubsystemId(Long jobId);

    List<Long> getJobIds(List<Long> subsystemIds);

    /**
     * 添加流水线成员
     * @param instanceUser key-资源实例id（流水线id），value-成员id列表
     */
    void addPermission(Map<Long, List<Long>> instanceUser);

    /**
     * 批量保存流水线成员角色
     *
     * @param jobIds 流水线jobId列表
     * @param userIds  用户列表
     * @param roleId  流水线角色id
     */
    void savePermission(List<Long> jobIds, List<Long> userIds, Long roleId);

    void deletePermission(Map<Long, List<Long>> instanceUser);

    /**
     * 批量移除流水线权限
     *
     * @param jobIds
     * @param userIds
     */
    void deletePermission(List<Long> jobIds, List<Long> userIds);

    /**
     * 批量移除流水线权限
     *
     * @param subIds
     * @param userIds
     */
    void deletePermission(Long subIds, List<Long> userIds);

    /**
     * 根据参数删除关联关系
     * @param subsystemId
     */
    Boolean removeByParams(Long subsystemId);

    /**
     * 根据参数删除关联关系
     *
     * @param subsystemId 子系统id
     * @param subsystemId 流水线id
     */
    Boolean removeByParams(Long subsystemId, Long jobId);

    /**
     * 根据参数删除关联关系
     *
     * @param jobIds
     */
    Boolean removeByParams(List<Long> jobIds);

    /**
     * 查询jobId
     *
     * @param subsystemIds
     */
    List<Long> listJobIdByParam(List<Long> subsystemIds);


    SubSystemPipeline getbyrequest(PipelineBindingRequest request);
}
