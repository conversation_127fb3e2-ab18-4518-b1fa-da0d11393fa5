package cn.harmonycloud.development.outbound;

import cn.harmonycloud.development.pojo.entity.SubSystemConfig;
import cn.harmonycloud.mybatis.base.BaseRepository;

import java.util.List;


public interface SubSystemConfigRepository extends BaseRepository<SubSystemConfig> {


    SubSystemConfig getByParams(Long subsystemId);

    void removeBySubIds(List<Long> subIds);

    List<SubSystemConfig> listByParams(int clearFlag);
}
