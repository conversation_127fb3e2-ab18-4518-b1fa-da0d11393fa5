package cn.harmonycloud.development.outbound;


import cn.harmonycloud.development.pojo.dto.system.SystemPageQuery;
import cn.harmonycloud.development.pojo.entity.DevopsSystem;
import cn.harmonycloud.mybatis.base.BaseRepository;
import cn.harmonycloud.pmp.model.entity.User;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import javax.annotation.Nullable;
import java.util.Collection;
import java.util.List;
import java.util.Map;

public interface DevopsSystemRepository extends BaseRepository<DevopsSystem> {

    /**
     * 系统列表
     *
     * @param fullName 系统名称（模糊查询）
     * @param sysCode 系统编码
     * @return
     */
    List<DevopsSystem> listByParams(String fullName, String sysCode);

    /**
     * 系统列表
     *
     * @param fullName 系统名称
     * @return
     */
    List<DevopsSystem> listByParams(@Nullable String fullName);

    /**
     * 系统列表
     * @param projectId
     * @return
     */
    List<DevopsSystem> listByProject(Long projectId);

    /**
     * 通用分页查询
     *
     * @param query
     * @return
     */
    Page<DevopsSystem> pageGeneral(SystemPageQuery query);

    /**
     * 逻辑删除系统数据
     *
     * @param id
     */
    void removeLogic(Long id, User currentUser);

    List<DevopsSystem> listByParams(List<Long> systemIds);

    /**
     * 根据系统id查询map数据
     *
     * @param systemIds
     * @return
     */
    Map<Long, DevopsSystem> mapByIds(List<Long> systemIds);

    /**
     * 关闭置顶
     *
     * @param systemId
     */
    void closeTop(Long systemId, User currentUser);

    /**
     * 置顶
     *
     * @param systemId
     */
    void topById(Long systemId, User currentUser);

    /**
     * 跨租户查询系统系统
     *
     * @param systemIds
     * @return
     */
    List<DevopsSystem> listByParamsNoTenant(List<Long> systemIds);
}
