package cn.harmonycloud.development.outbound;

import cn.harmonycloud.development.outbound.api.dto.project.IssuesDto;
import cn.harmonycloud.issue.model.dto.v2.IssuesQueryDTO;
import cn.harmonycloud.pmp.model.entity.User;
import cn.harmonycloud.project.model.dto.IssuesDTO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.List;

/**
 * 需求任务仓储
 */
public interface IssuesRepository {


    /**
     * 根据需求id查询需求列表
     *
     * @param taskIds 任务id列表
     * @return
     */
    List<IssuesDto> listTask(List<Long> taskIds);

    /**
     * 项目id查询需求列表
     *
     * @param projectId           项目id
     * @param directors           负责人id列表
     * @param issuesClassicIdList 分类列表   1:需求 2:任务 3:缺陷
     * @param isBase              基本信息   true 默认查集成信息   false-正常查询
     * @return
     */
    List<IssuesDto> listTask(Long projectId, List<Long> directors, List<Long> issuesClassicIdList, Boolean isBase);

    /**
     * 项目分页
     *
     * @param projectId 项目id
     * @param title     标题
     * @param taskIds   需求id列表
     * @param page
     * @param size
     * @return
     */
    Page<IssuesDto> pageTask(Long projectId, String title, Long sprintId, Long versionId ,Long issuesTypeId, Long statusId, Long priorityId, List<Long> principalIdList, List<Long> taskIds, int page, int size);

    /**
     * 查询跟踪事项包括拓展字段信息
     *
     * @param issuesTypeCode
     * @param ids
     */
    List<IssuesDTO> getListByIds(String issuesTypeCode, List<Long> ids);

    Boolean updateIssuesOfSys(IssuesDTO issuesDTO, User currentUser);

    List<IssuesDTO> listIssues(IssuesQueryDTO issuesQueryDTO);

    void deleteIssues(Long id);
}
