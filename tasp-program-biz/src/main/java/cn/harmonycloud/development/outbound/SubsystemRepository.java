package cn.harmonycloud.development.outbound;


import cn.harmonycloud.development.pojo.dto.subsystem.SubsystemGeneralQuery;
import cn.harmonycloud.development.pojo.entity.DevopsSubSystem;
import cn.harmonycloud.mybatis.base.BaseRepository;
import cn.harmonycloud.pmp.model.entity.User;
import cn.harmonycloud.pojo.subsystem.SubsystemDto;

import java.util.List;
import java.util.Map;

public interface SubsystemRepository extends BaseRepository<DevopsSubSystem> {

    /**
     * 根据参数查询子系统列表
     *
     * @param subsystemIds
     * @param systemIds
     * @return
     */
    List<DevopsSubSystem> listByParams(List<Long> subsystemIds, List<Long> systemIds);

    /**
     * 根据参数查询子系统列表
     *
     * @param systemId
     * @param ids
     * @param fullNameCn
     * @return
     */
    List<DevopsSubSystem> listByParams(Long systemId, List<Long> ids, String fullNameCn);

    /**
     * 根据参数查询子系统列表
     *
     * @param ids
     * @return
     */
    List<DevopsSubSystem> listByParams(List<Long> ids);

    /**
     * 根据系统id查询列表
     *
     * @param systemId
     */
    List<DevopsSubSystem> listBySystemId(Long systemId);

    /**
     * 根据子系统编码查询子系统信息
     *
     * @param subSystemCode
     * @return
     */
    DevopsSubSystem getByParams(String subSystemCode);

    /**
     * 查询子系统列表
     *
     * @param subSystemName
     * @param isLike
     * @return
     */
    List<DevopsSubSystem> listByParams(String subSystemName, boolean isLike);

    /**
     * 查询子系统列表
     *
     *
     * @param systemId
     * @param subCode
     * @return
     */
    List<DevopsSubSystem> listByCode(Long systemId, String subCode);

    List<DevopsSubSystem> listByParams(Long systemId, String fullNameCn);

    boolean removeLogic(Long id, User currentUser);

    List<DevopsSubSystem> listByParams(SubsystemGeneralQuery subsystemGeneralQuery);

    Map<Long, DevopsSubSystem> mapByIds(List<Long> subIds);

    /**
     * 查询所有子系统
     *
     * @return
     */
    List<DevopsSubSystem> listAll();

    boolean batchRemoveLogic(List<Long> ids, User currentUser);
}
