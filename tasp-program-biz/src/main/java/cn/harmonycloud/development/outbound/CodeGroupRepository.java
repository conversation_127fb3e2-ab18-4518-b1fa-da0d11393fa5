package cn.harmonycloud.development.outbound;

import cn.harmonycloud.development.outbound.api.dto.scm.GroupDto;
import cn.harmonycloud.development.outbound.api.dto.scm.GroupMemberDto;
import cn.harmonycloud.development.outbound.api.dto.scm.GroupRequest;
import cn.harmonycloud.development.outbound.api.dto.scm.GroupResponse;

import java.util.List;

public interface CodeGroupRepository {

    /**
     * 创建分组
     * @param request
     * @return
     */
    GroupResponse createGroup(GroupRequest request);

    /**
     * 获取代码分组详情
     * @param groupId 分组id
     */
    GroupDto group(Integer groupId, String path);

    /**
     * 获取代码分组列表
     * @return
     */
    List<GroupDto> getGroups();

    /**
     * 添加组织成员
     * @param groupId 分组id
     * @param groupId
     * @param members
     */
    void addGroupMember(Integer groupId, List<GroupMemberDto> members);

    /**
     * 删除组织成员
     * @param groupId 组织id
     * @param username 用户名
     */
    void deleteGroupMember(Integer groupId, String username);

    /**
     * 更新分组名称
     *
     * @param groupId
     * @param systemName
     */
    void modifyGroupName(Integer groupId, String systemName);
}
