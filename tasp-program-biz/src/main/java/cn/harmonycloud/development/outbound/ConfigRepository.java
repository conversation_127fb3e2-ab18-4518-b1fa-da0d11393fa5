package cn.harmonycloud.development.outbound;

import cn.harmonycloud.development.outbound.api.dto.config.ConfigCreateDTO;
import cn.harmonycloud.development.outbound.api.dto.config.ConfigEnvCreateDTO;

public interface ConfigRepository {

    /**
     * 新建一个配置库
     *
     * @return
     */
    Long createConfig(ConfigCreateDTO configCreateDTO);

    /**
     * 新建一个配置环境
     *
     * @param configEnvCreateDTO
     */
    void createEnv(ConfigEnvCreateDTO configEnvCreateDTO);
}
