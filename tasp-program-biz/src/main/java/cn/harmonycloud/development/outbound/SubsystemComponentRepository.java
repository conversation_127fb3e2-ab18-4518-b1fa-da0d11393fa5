package cn.harmonycloud.development.outbound;

import cn.harmonycloud.development.pojo.entity.SubSystemComponent;
import cn.harmonycloud.mybatis.base.BaseRepository;

import java.util.List;
import java.util.Map;

public interface SubsystemComponentRepository extends BaseRepository<SubSystemComponent> {

    List<SubSystemComponent> listByParam(String component, String componentKey);

    List<SubSystemComponent> listByParam(Long subSystemId, String component);

    List<SubSystemComponent> listByParam(List<Long> subSystemIds, String component);

    Map<Long, List<SubSystemComponent>> mapComponent(List<Long> subSystemIds, String component);

    List<SubSystemComponent> listByParam(Long subSystemId, String component, String componentKey);

    boolean removeByParams(String component, String componentKey);

    /**
     * 子系统关联组件列表
     *
     * @param componentType 组件类型
     * @param componentKeys 组件key
     * @return
     */
    List<SubSystemComponent> listByParam(String componentType, List<String> componentKeys);

    /**
     * 删除子系统组件
     *
     * @param subsystemId 子系统id
     */
    void deleteComponent(Long subsystemId);

    /**
     * 删除子系统组件
     *
     * @param systemId 子系统id
     */
    void deleteComponentBySystemId(Long systemId);
}
