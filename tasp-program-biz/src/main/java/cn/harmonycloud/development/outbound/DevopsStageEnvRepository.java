package cn.harmonycloud.development.outbound;

import cn.harmonycloud.development.pojo.dto.devopsstage.DevopsStageEnvListRequest;
import cn.harmonycloud.development.pojo.entity.DevopsStageEnv;
import cn.harmonycloud.mybatis.base.BaseRepository;

import java.util.List;
import java.util.Map;

public interface DevopsStageEnvRepository extends BaseRepository<DevopsStageEnv> {

    /**
     * 查询子系统环境列表
     * @param request
     */
    List<DevopsStageEnv> envList(DevopsStageEnvListRequest request);

    /**
     * 查询子系统环境列表
     * @param subsystemId
     * @param stageId
     * @param envName
     * @return
     */
    List<DevopsStageEnv> envList(Long subsystemId, Long stageId, String envName);

    /**
     * 查询子系统环境列表
     * @param subsystemId 子系统id
     * @param envCode 阶段环境编码
     * @return
     */
    List<DevopsStageEnv> envList(Long subsystemId, String envCode);

    /**
     * 删除环境
     * @param stageEnvId 环境id
     * @param logic 逻辑删除 true-物理删除；false-逻辑删除
     * @param userId 操作用户
     */
    boolean delete(Long stageEnvId, boolean logic, Long userId);

    /**
     * 根据部署资源Id查询使用的环境列表
     *
     *
     * @param subSystemIds
     * @param deployEnv
     * @param deployIds 部署资源id
     * @return
     */
    Map<Integer,List<DevopsStageEnv>> envMap(List<Long> subSystemIds, Integer deployEnv, List<Integer> deployIds);

    /**
     * 查询子系统环境列表
     * @param subIds 子系统id列表
     * @return
     */
    List<DevopsStageEnv> envList(List<Long> subIds);

    void deleteByStageIds(List<Long> deleteOld, Long userId);
}
