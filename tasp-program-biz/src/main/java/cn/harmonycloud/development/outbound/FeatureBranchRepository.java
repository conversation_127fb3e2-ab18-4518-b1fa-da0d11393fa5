package cn.harmonycloud.development.outbound;

import cn.harmonycloud.development.pojo.entity.FeatureBranch;
import cn.harmonycloud.development.pojo.vo.feature.FeatureBranchQuery;
import cn.harmonycloud.mybatis.base.BaseRepository;

import java.util.List;

public interface FeatureBranchRepository extends BaseRepository<FeatureBranch> {


    /**
     * 根据参数查询特性关联分支信息
     *
     * @param featureId
     * @return
     */
    FeatureBranch getByParam(Long featureId);

    /**
     * 根据参数查询列表
     *
     * @param featureIds
     * @return
     */
    List<FeatureBranch> listByParam(List<Long> featureIds);

    /**
     *
     * @param query
     * @return
     */
    List<FeatureBranch> listBranchByQuery(FeatureBranchQuery query);

    /**
     * 查询子系统下所有分支列表
     * @param subsystemId 子系统id
     * @return
     */
    List<FeatureBranch> listByParam(Long subsystemId);

    /**
     * 更据参数删除分支信息
     *
     * @param subsystemIds
     */
    void removeByParams(List<Long> subsystemIds);

    /**
     * 删除分支信息
     *
     */
    Boolean removeById(Long id, Boolean logic);

    /**
     * 根据分支名称查询
     *
     * @param branches
     * @return
     */
    List<FeatureBranch> listByBranches(Long subsystemId, List<String> branches);

    /**
     * 根据分支名称查询
     *
     * @param
     * @return
     */
    void clear(Long featureId, String clearCommit, String clearDevCommit, Long userId);
}
