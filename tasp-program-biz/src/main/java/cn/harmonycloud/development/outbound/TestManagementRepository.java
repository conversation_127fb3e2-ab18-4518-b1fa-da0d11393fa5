package cn.harmonycloud.development.outbound;

import cn.harmonycloud.development.pojo.dto.system.EnvTestDTO;
import cn.harmonycloud.development.pojo.dto.test.TestBatchModifyRequest;
import cn.harmonycloud.development.pojo.dto.test.TestModifyRequest;
import cn.harmonycloud.development.pojo.entity.TestManagement;
import cn.harmonycloud.development.pojo.vo.test.TestManagementPageQuery;
import cn.harmonycloud.mybatis.base.BaseRepository;
import cn.harmonycloud.pmp.model.entity.User;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.List;

public interface TestManagementRepository extends BaseRepository<TestManagement> {

    Page<TestManagement> queryPage(EnvTestDTO envTestDTO);

    List<TestManagement> listByParams(Long subsystemId, Long stageEnvId);

    List<TestManagement> listByParams(Long buildInstanceId);

    void modify(TestModifyRequest modifyRequest, User currentUser);

    void modify(TestBatchModifyRequest modifyRequest, User currentUser);

    Page<TestManagement> pageQuery(TestManagementPageQuery query);
}
