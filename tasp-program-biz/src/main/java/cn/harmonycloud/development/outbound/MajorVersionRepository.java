package cn.harmonycloud.development.outbound;

import cn.harmonycloud.development.pojo.entity.MajorVersion;
import cn.harmonycloud.development.pojo.entity.VersionSubsystem;
import cn.harmonycloud.development.pojo.vo.version.MajorVersionQuery;
import cn.harmonycloud.development.pojo.vo.version.VersionQuery;
import cn.harmonycloud.development.pojo.vo.version.VersionSubsystemQuery;
import cn.harmonycloud.mybatis.base.BaseRepository;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.List;

public interface MajorVersionRepository extends BaseRepository<MajorVersion> {
    /**
     * 根据参数获取大版本列表
     * @param subSystemId
     * @param majorVersionNumber
     * @return
     */
    List<MajorVersion> listByParams(Long subSystemId, String majorVersionNumber);

    Page<MajorVersion> pageQuery(MajorVersionQuery request);

    void deleteLogic(Long majorVersionId);

    List<MajorVersion> listBySubSystemId(Long subSystemId);

    List<MajorVersion> listByParams(List<Long> ids, List<Long> subsystemIds);

    List<MajorVersion> listByParams(Long subSystemId, Integer limit);

    boolean optimisticUpdates(Long id, String subVersionNumber, Long currentUserId, String oldSubNumber);

    /**
     * 版本、子系统信息关联查询
     *
     * @param query
     * @return
     */
    List<VersionSubsystem> versionSubsystemList(VersionSubsystemQuery query);
}
