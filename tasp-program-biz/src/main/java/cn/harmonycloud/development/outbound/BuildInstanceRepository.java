package cn.harmonycloud.development.outbound;

import cn.harmonycloud.development.pojo.entity.BuildInstance;
import cn.harmonycloud.mybatis.base.BaseRepository;

import javax.annotation.Nullable;
import java.util.List;

public interface BuildInstanceRepository extends BaseRepository<BuildInstance> {
    BuildInstance getByBuildId(Long buildId);

    BuildInstance getLastOneByParam(Long stageEnvId, Long jobId);

    BuildInstance getLastOneByParam(Long stageEnvId, Long jobId, @Nullable Long versionId);

    BuildInstance getLastOneByParam(Long stageEnvId);

    /**
     * 查询最近一次构建记录
     *
     * @param stageEnvId
     * @param versionId wei空时表示查询空的最近一次记录
     * @return
     */
    BuildInstance getLastOneByParamOrNull(Long stageEnvId, @Nullable Long versionId);

    void saveMergeTask(Long id, Long mergeGroupId, Long mergeTaskId, List<Long> featureIds, List<String> branches);

    /**
     * 将制定版本设置成空
     *
     * @param versionId
     */
    void removeVersion(Long versionId);
}
