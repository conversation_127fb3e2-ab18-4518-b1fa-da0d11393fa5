package cn.harmonycloud.development.outbound;

import cn.harmonycloud.pmp.model.entity.User;
import cn.harmonycloud.pmp.model.vo.UserVo;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.List;
import java.util.Map;

/**
 * 人员角色权限管理
 */
public interface IamRepository {

    /**
     * 获取当前登录人
     *
     * @return
     */
    User getCurrentUser();

    /**
     * 根据用户id查询用户信息
     *
     * @param userId
     * @return
     */
    User getUserById(Long userId);

    /**
     * 根据id列表查询用户集合
     *
     * @param userIds
     * @return map集合 k-userId；v-用户信息
     */
    Map<Long, User> listUserByIdsForMap(List<Long> userIds);

    /**
     * 根据id列表查询用户集合
     *
     * @param userIds
     * @return
     */
    List<User> listUserByIds(List<Long> userIds);

    /**
     * 当前租户下的人员列表
     *
     * @return
     */
    List<UserVo> usersByCurrentOrigin(String queryParam);

    /**
     * 资源下的成员列表
     *
     * @param resourceTypeCode
     * @param resourceInstanceId
     * @return
     */
    List<UserVo> resourceMember(String resourceTypeCode, Long resourceInstanceId);

    /**
     * 资源下的管理员列表
     *
     * @param resourceTypeCode
     * @param resourceInstanceId
     * @return
     */
    List<UserVo> resourceMemberAdmin(String resourceTypeCode, Long resourceInstanceId);

    /**
     * 当前租户下的人员列表(排除已在资源实例中的成员)
     *
     * @return
     */
    List<UserVo> usersByCurrentOriginOver(String resourceTypeCode, Long instanceId, String queryParam);

    /**
     * 排除用户
     *
     * @param allUser
     * @param overUser
     * @return
     */
    List<UserVo> userOver(List<UserVo> allUser, List<UserVo> overUser);

    /**
     * 根据资源类型查询角色列表
     *
     * @param resourceTypeCode
     * @return
     */
    Page<UserVo> pageMember(String resourceTypeCode, Long resourceInstanceId, int current, int size, String queryParam);

    /**
     * 删除成员
     *
     * @return
     */
    Boolean deleteMember(String resourceTypeCode, Long instanceId, Long userId);

}
