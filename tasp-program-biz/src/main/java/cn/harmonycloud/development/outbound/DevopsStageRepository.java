package cn.harmonycloud.development.outbound;

import cn.harmonycloud.development.pojo.entity.DevopsStage;
import cn.harmonycloud.mybatis.base.BaseRepository;

import java.util.List;

public interface DevopsStageRepository extends BaseRepository<DevopsStage> {

    /**
     * 根据子系统id查询所有阶段
     * @param subsystemId
     * @return
     */
    List<DevopsStage> listBySubsystemId(Long subsystemId);


    /**
     *
     *
     * @param subsystemIds
     * @param envId
     * @param newEnvId
     */
    void updateEnvId(List<Long> subsystemIds, Integer envId, Integer newEnvId);
}
