package cn.harmonycloud.development.outbound;

import cn.harmonycloud.development.outbound.api.dto.promotion.*;
import cn.harmonycloud.development.pojo.dto.product.DevopsProductMetadataDto;
import cn.harmonycloud.development.pojo.dto.product.ProductTreeDto;
import cn.harmonycloud.development.pojo.dto.product.ProductTreeRequestDto;
import cn.harmonycloud.development.pojo.dto.version.EnvironmentDTO;
import cn.harmonycloud.development.pojo.entity.PromotionInstanceDto;
import cn.harmonycloud.development.pojo.vo.repository.CreateInstanceReq;
import cn.harmonycloud.development.pojo.vo.repository.ListInstanceReq;
import cn.harmonycloud.development.pojo.vo.repository.ProductPageQuery;
import cn.harmonycloud.development.pojo.entity.PromotionNodeInstance;
import cn.harmonycloud.development.pojo.vo.version.ProductPromotionVo;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

public interface ProductRepository {


    /**
     * 制品列表以版本分组的树形结构
     *
     * @param request
     * @return
     */
    List<ProductTreeDto> tree(ProductTreeRequestDto request);

    /**
     * 制品版本列表
     *
     * @param systemId     系统id
     * @param repoIds      仓库id
     * @param subsystemIds 子系统id列表
     * @return
     */
    List<String> versionList(Long systemId, List<Long> repoIds, List<Long> subsystemIds, Integer kind);

    /**
     * 上传制品
     *
     * @param files         制品文件
     * @param repositoryId  仓库id
     * @param directory     路径
     * @param assetFilename 制品名称
     * @return
     */
    void upload(MultipartFile[] files, Integer repositoryId, String directory, String assetFilename);

    /**
     * 制品晋级
     *
     * @param r
     * @return
     */
    @Deprecated
    void promoteProduct(ProductPromotionVo r);

    /**
     * 根据元数据查询制品
     */
    @Deprecated
    List<DetailDTO> getProductPromotionDetail(PromotionDetailRequest p);

    /**
     * 制品晋级环境查询
     *
     * @return
     */
    @Deprecated
    List<EnvironmentDTO> getProductPromotionEnvironment();

    /**
     * 更新制品的元数据信息
     *
     * @param req
     */
    void updateMetadataByBuildId(ProductMetadataDto req);

    /**
     * 制品列表
     *
     * @param subSystemId
     * @param versionNumber
     * @param format
     */
    List<DevopsProductMetadataDto> productList(Long subSystemId, String versionNumber, String format, Integer kind);


    List<DevopsProductMetadataDto> list(ProductPageQuery productPageQuery);

    List<DevopsProductMetadataDto> list(Long repoId, Long subsystemId, String format, List<String> versions);

    CreateInstanceRsp createInstance(CreateInstanceReq createInstanceReq);

    List<PromotionInstanceDto> listPromotionInstance(ListInstanceReq listInstanceReq);


    /**
     * 根据制品id查询制品列表
     *
     * @param ids 制品ids
     * @return
     */
    List<DevopsProductMetadataDto> listByIds(List<Long> ids);

    /**
     * 查询制品
     *
     * @param repoId
     * @param productName
     * @param productVersion
     * @return
     */
    DevopsProductMetadataDto get(Long repoId, String productName, String productVersion);
}
