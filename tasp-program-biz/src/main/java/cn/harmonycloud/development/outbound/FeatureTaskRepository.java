package cn.harmonycloud.development.outbound;

import cn.harmonycloud.development.pojo.entity.FeatureTask;
import cn.harmonycloud.mybatis.base.BaseRepository;

import java.util.List;
import java.util.Map;

public interface FeatureTaskRepository extends BaseRepository<FeatureTask> {


    Map<Long, List<FeatureTask>> mapByFeatureId(List<Long> featureIds);

    List<FeatureTask> listByFeatureId(List<Long> featureId);

    List<Long> listIdByFeatureId(List<Long> featureId);
}
