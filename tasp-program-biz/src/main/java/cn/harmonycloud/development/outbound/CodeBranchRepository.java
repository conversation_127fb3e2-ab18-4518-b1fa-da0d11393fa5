package cn.harmonycloud.development.outbound;

import cn.harmonycloud.development.outbound.api.dto.coderepo.*;

import java.util.List;
import java.util.Map;

public interface CodeBranchRepository {

    /**
     * 查询分支列表
     * @param gitlabId 代码仓库id
     * @param search 搜索
     * @return
     */
    List<BranchDto> getBranch(Integer gitlabId, String search);

    /**
     * 查询分支详情
     * @param gitlabId 代码仓库id
     * @param branchName 分支名称
     * @return
     */
    BranchDto detailsBranch(Integer gitlabId, String branchName);

    /**
     * 创建代码仓库分支
     * @param name 分支名称
     * @param gitlabId 代码仓库id
     * @param sourceBranch 源分支
     * @param isProtected 是否保护分支
     */
    BranchDto createBranch(String name, Integer gitlabId, String sourceBranch, Boolean isProtected);

    /**
     * 查询源分支和目标分支的差异
     *
     * @param gitlabId 代码仓库id
     * @param sourceBranch
     * @param targetBranch
     * @return
     */
    Map<String, BranchStageDto> listBranchStage(Integer gitlabId, List<String> sourceBranch, String targetBranch);

    /**
     * 查询tag列表
     *
     * @param gitlabId 代码仓库id
     * @param tagName 版本名称
     * @return
     */
    List<RefDto> listTag(Integer gitlabId, String tagName);

    /**
     * 删除分支
     *
     * @param gitlabId
     * @param branchName
     */
    void removeBranch(Integer gitlabId, String branchName);

    /**
     * 查询分支以及标签列表
     *
     * @param gitlabId
     * @return
     */
    List<String> listBranchAndTag(Integer gitlabId);

    /**
     * 查询临时分支列表
     *
     * @param gitlabId
     */
    List<TempBranchVO> listTempBranch(Integer gitlabId);
}
