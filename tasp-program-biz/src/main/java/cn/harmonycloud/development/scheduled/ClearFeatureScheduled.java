package cn.harmonycloud.development.scheduled;

import cn.harmonycloud.development.outbound.*;
import cn.harmonycloud.development.pojo.entity.DevopsFeature;
import cn.harmonycloud.development.pojo.entity.FeatureBranch;
import cn.harmonycloud.development.pojo.entity.SubSystemComponent;
import cn.harmonycloud.development.pojo.entity.SubSystemConfig;
import cn.harmonycloud.development.service.CodeRepoService;
import cn.harmonycloud.development.service.FeatureService;
import cn.harmonycloud.enums.SubSystemComponentEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/8/10 4:31 下午
 **/
@Slf4j
@Component
//@ConditionalOnProperty(prefix = "xxl.job", name = "enable", havingValue = "true")
public class ClearFeatureScheduled {

    @Autowired
    private FeatureRepository featureRepository;
    @Autowired
    private FeatureBranchRepository featureBranchRepository;
    @Autowired
    private CodeBranchRepository codeBranchRepository;
    @Autowired
    private CodeRepoService codeRepoService;
    @Autowired
    private FeatureService featureService;
    @Autowired
    private SubSystemConfigRepository subSystemConfigRepository;
    @Autowired
    private SubsystemComponentRepository subsystemComponentRepository;

    @Value("${biz.feature.clearCycle:7}")
    private Long clearCycle;

   // @XxlJob("featureClear")
//    public ReturnT<String> featureClear(String date) {
//        clearFeature();
//        clearMergeBranch();
//        return ReturnT.SUCCESS;
//    }

    /**
     * 清理合并请求的临时分支
     *
     */
    private void clearMergeBranch() {
        // 所有开启配置的子系统
        List<SubSystemConfig> subSystemConfigs = subSystemConfigRepository.listByParams(1).stream().filter(c -> c.getClearTemporaryBranchTime() != null).collect(Collectors.toList());
        List<Long> subIds = subSystemConfigs.stream().map(SubSystemConfig::getSubsystemId).collect(Collectors.toList());
        Map<Long, List<SubSystemComponent>> mapComponent = subsystemComponentRepository.mapComponent(subIds, SubSystemComponentEnum.GITLAB.getComponent());
        for (SubSystemConfig subSystemConfig : subSystemConfigs) {
            if (!mapComponent.containsKey(subSystemConfig.getSubsystemId())){
                continue;
            }
            List<SubSystemComponent> components = mapComponent.get(subSystemConfig.getSubsystemId());
            if (components.size() > 1){
                log.error("feature_clear: more than one gitlab component");
                continue;
            }
            Integer codeRepoId = Integer.parseInt(components.get(0).getComponentKey());
            try {
                Integer days = subSystemConfig.getClearTemporaryBranchTime() > 0 ? subSystemConfig.getClearTemporaryBranchTime() : 0;
                codeRepoService.clearMergeBranch(codeRepoId, subSystemConfig.getClearTemporaryBranchTime());
            }catch (Exception e){
                e.printStackTrace();
                log.error("feature_clear: clearMergeBranch error", e);
            }
        }
    }

    /**
     * 清理在版本中发布的功能分支
     *
     */
    private void clearFeature() {
        List<DevopsFeature> devopsFeatures = listClearFeature();
        if (CollectionUtils.isEmpty(devopsFeatures)) {
            log.debug("feature_clear: no data");
            return ;
        }
        List<Long> featureIds = devopsFeatures.stream().map(df -> df.getId()).collect(Collectors.toList());
        List<FeatureBranch> featureBranches = featureBranchRepository.listByParam(featureIds);
        Map<Long, FeatureBranch> mapBranch = featureBranches.stream().collect(Collectors.toMap(FeatureBranch::getFeatureId, f -> f));
        for (DevopsFeature devopsFeature : devopsFeatures) {
            FeatureBranch featureBranch = mapBranch.get(devopsFeature.getId());
            Integer codeRepoIdBySystemId = codeRepoService.getCodeRepoIdBySystemId(devopsFeature.getSubSystemId());
            featureService.deleteBranch(devopsFeature, featureBranch, codeRepoIdBySystemId, null);
        }
    }

    public List<DevopsFeature> listClearFeature() {
        Long time = 0L - clearCycle;
        LocalDateTime clearTime = LocalDateTime.now().plusDays(time);
        return featureRepository.listBeforeReleaseTime(clearTime, 1);
    }

}
