package cn.harmonycloud.development.execption.thgn;

import cn.harmonycloud.enums.ExceptionCode;
import cn.harmonycloud.exception.TrinasolarException;

/**
 * <AUTHOR>
 */
public class GitException extends TrinasolarException {

    public GitException(ExceptionCode exceptionCode) {
        super(exceptionCode);
    }

    public GitException(String msg) {
        super(ExceptionCode.INNER_EXCEPTION, msg);
    }

    public GitException(ExceptionCode exceptionCode, String msg) {
        super(exceptionCode, msg);
    }

    public GitException(ExceptionCode exceptionCode, int code, String msg) {
        super(exceptionCode, code, msg);
    }
}
