//package cn.harmonycloud.hzbank.inbound.controller;
//
//import cn.harmonycloud.hzbank.Application;
//import cn.harmonycloud.hzbank.outbound.util.HuToolHttpUtil;
//import org.junit.jupiter.api.Test;
//import org.junit.jupiter.api.extension.ExtendWith;
//import org.junit.runner.RunWith;
//import org.mockito.MockedStatic;
//import org.mockito.Mockito;
//import org.mockito.junit.jupiter.MockitoExtension;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.test.context.junit4.SpringRunner;
//
//import javax.annotation.Resource;
//import java.util.HashMap;
//import java.util.Map;
//
//import static org.junit.jupiter.api.Assertions.*;
//
///**
// * <AUTHOR>
// * @Date 2022/8/12
// */
//@ExtendWith(MockitoExtension.class)
//@RunWith(SpringRunner.class)
//@SpringBootTest(classes = Application.class)
//class PmsControllerTest {
//    MockedStatic<HuToolHttpUtil> aStatic = Mockito.mockStatic(HuToolHttpUtil.class);
//
//    @Resource
//    PmsController pmsController;
//
//    @Test
//    void syncUser() {
//    }
//
//    @Test
//    void syncYesterdayUser() {
//    }
//
//    @Test
//    void login() {
//    }
//
//    @Test
//    void createUser() {
//    }
//}