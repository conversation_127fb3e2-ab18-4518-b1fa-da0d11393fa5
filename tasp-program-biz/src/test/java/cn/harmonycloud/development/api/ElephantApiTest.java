//package cn.harmonycloud.hzbank.api;
//
//import cn.harmonycloud.common.core.base.BaseResult;
//import cn.harmonycloud.hzbank.Application;
//import cn.harmonycloud.hzbank.inbound.controller.ElephantController;
//import cn.harmonycloud.hzbank.outbound.util.HuToolHttpUtil;
//import cn.harmonycloud.hzbank.pojo.dto.elephant.ProjectDTO;
//import cn.harmonycloud.hzbank.pojo.vo.system.SubSystemVO;
//import cn.harmonycloud.hzbank.pojo.vo.system.SystemVO;
//import com.alibaba.fastjson.JSON;
//import org.junit.jupiter.api.Test;
//import org.junit.jupiter.api.extension.ExtendWith;
//import org.junit.runner.RunWith;
//import org.mockito.MockedStatic;
//import org.mockito.Mockito;
//import org.mockito.junit.jupiter.MockitoExtension;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.test.context.junit4.SpringRunner;
//
//import javax.annotation.Resource;
//import java.util.HashMap;
//import java.util.List;
//import java.util.Map;
//
///**
// * <AUTHOR>
// * @Date 2022/7/26
// */
//@ExtendWith(MockitoExtension.class)
//@RunWith(SpringRunner.class)
//@SpringBootTest(classes = Application.class)
//class ElephantApiTest {
//    @Resource
//    ElephantController elephantController;
//
//    MockedStatic<HuToolHttpUtil> aStatic = Mockito.mockStatic(HuToolHttpUtil.class);
//    @Test
//    void login() {
//        String login = "";
//        Map<String, Object> map = new HashMap<>();
//        map.put("password", "public");
//        map.put("username", "2f74811300c361e53b430611a7d1769f");
//        aStatic.when(() -> HuToolHttpUtil.sentGetCookie("巨象平台", "http://*************:8000/pms/openIM/loginCheck", map,"s")).thenReturn(login);
//    }
//
//    @Test
//    void listAllSystem() {
//        login();
//        String s = "{\"code\":0,\"msg\":null,\"data\":[{\"id\":1,\"orderNo\":1,\"code\":\"sys_depart\",\"name\":\"depat\",\"nameCn\":\"系统体系\",\"fnameCn\":\"体系\",\"descCn\":\"体\",\"status\":\"01\",\"createTime\":null,\"modifiedTime\":null,\"sysgroupInfos\":[{\"id\":null,\"orderNo\":null,\"code\":null,\"name\":null,\"nameCn\":null,\"fnameCn\":null,\"descCn\":null,\"status\":null,\"createTime\":null,\"modifiedTime\":null,\"sysInfos\":[{\"id\":1,\"orderNo\":1,\"code\":null,\"name\":null,\"nameCn\":null,\"fnameCn\":null,\"descCn\":null,\"nickname\":null,\"architect\":null,\"techDept\":null,\"busiDept\":null,\"ip\":null,\"status\":null,\"director\":null,\"buildDate\":null,\"createTime\":null,\"modifiedTime\":null}]}]},{\"id\":1,\"orderNo\":1,\"code\":\"sys_depart\",\"name\":\"depat\",\"nameCn\":\"系统体系\",\"fnameCn\":\"体系\",\"descCn\":\"体\",\"status\":\"01\",\"createTime\":null,\"modifiedTime\":null,\"sysgroupInfos\":[{\"id\":null,\"orderNo\":null,\"code\":null,\"name\":null,\"nameCn\":null,\"fnameCn\":null,\"descCn\":null,\"status\":null,\"createTime\":null,\"modifiedTime\":null,\"sysInfos\":[{\"id\":2,\"orderNo\":2,\"code\":null,\"name\":null,\"nameCn\":null,\"fnameCn\":null,\"descCn\":null,\"nickname\":null,\"architect\":null,\"techDept\":null,\"busiDept\":null,\"ip\":null,\"status\":null,\"director\":null,\"buildDate\":null,\"createTime\":null,\"modifiedTime\":null}]}]}]}";
//        aStatic.when(() -> HuToolHttpUtil.sentGet("巨象平台", "http://*************:8000/api/v1/arch","Cookie",null)).thenReturn(s);
//        String s1 = "[{\"id\":1,\"orderNo\":1,\"code\":\"sys_depart\",\"name\":\"depat\",\"nameCn\":\"系统体系\",\"fnameCn\":\"体系\",\"descCn\":\"体\",\"status\":\"01\",\"createTime\":null,\"modifiedTime\":null,\"sysgroupInfos\":[{\"id\":null,\"orderNo\":null,\"code\":null,\"name\":null,\"nameCn\":null,\"fnameCn\":null,\"descCn\":null,\"status\":null,\"createTime\":null,\"modifiedTime\":null,\"sysInfos\":[{\"id\":1,\"orderNo\":1,\"code\":null,\"name\":null,\"nameCn\":null,\"fnameCn\":null,\"descCn\":null,\"nickname\":null,\"architect\":null,\"techDept\":null,\"busiDept\":null,\"ip\":null,\"status\":null,\"director\":null,\"buildDate\":null,\"createTime\":null,\"modifiedTime\":null}]}]},{\"id\":1,\"orderNo\":1,\"code\":\"sys_depart\",\"name\":\"depat\",\"nameCn\":\"系统体系\",\"fnameCn\":\"体系\",\"descCn\":\"体\",\"status\":\"01\",\"createTime\":null,\"modifiedTime\":null,\"sysgroupInfos\":[{\"id\":null,\"orderNo\":null,\"code\":null,\"name\":null,\"nameCn\":null,\"fnameCn\":null,\"descCn\":null,\"status\":null,\"createTime\":null,\"modifiedTime\":null,\"sysInfos\":[{\"id\":2,\"orderNo\":2,\"code\":null,\"name\":null,\"nameCn\":null,\"fnameCn\":null,\"descCn\":null,\"nickname\":null,\"architect\":null,\"techDept\":null,\"busiDept\":null,\"ip\":null,\"status\":null,\"director\":null,\"buildDate\":null,\"createTime\":null,\"modifiedTime\":null}]}]}]";
//        aStatic.when(() -> HuToolHttpUtil.getStrFromData(s, "data")).thenReturn(s1);
//        BaseResult<List<SystemVO>> result = elephantController.listAllSystem();
//        System.out.println(JSON.toJSONString(result));
//    }
//
//    @Test
//    void listProjectByCode() {
//        login();
//        String s = "{\"data\":[{\"id\":\"8\",\"subsysCode\":\"SUBSYS_AFLOAN_FERACK\",\"projectName\":\"FunTrack\",\"description\":\"资金跟踪\",\"status\":\"0\",\"repository\":\"\"},{\"id\":\"9\",\"subsysCode\":\"SUBSYS_AFLOAN_FERACK\",\"projectName\":\"Fun\",\"description\":\"跟踪监测\",\"status\":\"0\",\"repository\":\"\"},{\"id\":\"1\",\"subsysCode\":\"HZBANK\",\"projectName\":\"Track\",\"description\":\"服务工程\",\"status\":\"0\",\"repository\":\"\"}]}";
//        aStatic.when(() -> HuToolHttpUtil.sentGet("巨象平台", "http://*************:8000/api/v1/subsysProject","Cookie",null)).thenReturn(s);
//        String s1 = "[{\"id\":\"8\",\"subsysCode\":\"SUBSYS_AFLOAN_FERACK\",\"projectName\":\"FunTrack\",\"description\":\"资金跟踪\",\"status\":\"0\",\"repository\":\"\"},{\"id\":\"9\",\"subsysCode\":\"SUBSYS_AFLOAN_FERACK\",\"projectName\":\"Fun\",\"description\":\"跟踪监测\",\"status\":\"0\",\"repository\":\"\"},{\"id\":\"1\",\"subsysCode\":\"HZBANK\",\"projectName\":\"Track\",\"description\":\"服务工程\",\"status\":\"0\",\"repository\":\"\"}]";
//        aStatic.when(() -> HuToolHttpUtil.getStrFromData(s, "data")).thenReturn(s1);
//        BaseResult<List<ProjectDTO>> result = elephantController.listProjectByCode("HZBANK");
//        System.out.println(JSON.toJSONString(result));
//    }
//
//    @Test
//    void listSubsystem() {
//        login();
//        String s2 = "{\"code\":0,\"msg\":null,\"data\":[{\"id\":1,\"orderNo\":1,\"code\":\"SUBSYS_CORE_OT\",\"name\":\"Bank Core System\",\"nameCn\":\"核心联机\",\"fnameCn\":\"核心联机交易系统\",\"descCn\":\"\",\"runLevel\":\"I类\",\"techDept\":\"开发四部\",\"busiDept\":null,\"status\":null,\"techDirector\":\"dep_zxb\",\"opsDirector\":\"wangbing\"},{\"id\":1,\"orderNo\":1,\"code\":\"HZBANK\",\"name\":\"Bank Core System\",\"nameCn\":\"核心\",\"fnameCn\":\"核心交易系统\",\"descCn\":\"\",\"runLevel\":\"I类\",\"techDept\":\"开发\",\"busiDept\":null,\"status\":null,\"techDirector\":\"dep_zxb\",\"opsDirector\":\"wangbing\"}]}";
//        aStatic.when(() -> HuToolHttpUtil.sentGet("巨象平台", "http://*************:8000/api/v1/system/HZBANK","Cookie",null)).thenReturn(s2);
//        String s3 = "[{\"id\":1,\"orderNo\":1,\"code\":\"SUBSYS_CORE_OT\",\"name\":\"Bank Core System\",\"nameCn\":\"核心联机\",\"fnameCn\":\"核心联机交易系统\",\"descCn\":\"\",\"runLevel\":\"I类\",\"techDept\":\"开发四部\",\"busiDept\":null,\"status\":null,\"techDirector\":\"dep_zxb\",\"opsDirector\":\"wangbing\"},{\"id\":1,\"orderNo\":1,\"code\":\"HZBANK\",\"name\":\"Bank Core System\",\"nameCn\":\"核心\",\"fnameCn\":\"核心交易系统\",\"descCn\":\"\",\"runLevel\":\"I类\",\"techDept\":\"开发\",\"busiDept\":null,\"status\":null,\"techDirector\":\"dep_zxb\",\"opsDirector\":\"wangbing\"}]";
//        aStatic.when(() -> HuToolHttpUtil.getStrFromData(s2, "data")).thenReturn(s3);
//        String s = "{\"data\":[{\"id\":\"8\",\"subsysCode\":\"SUBSYS_AFLOAN_FERACK\",\"projectName\":\"FunTrack\",\"description\":\"资金跟踪\",\"status\":\"0\",\"repository\":\"\"},{\"id\":\"9\",\"subsysCode\":\"SUBSYS_AFLOAN_FERACK\",\"projectName\":\"Fun\",\"description\":\"跟踪监测\",\"status\":\"0\",\"repository\":\"\"},{\"id\":\"1\",\"subsysCode\":\"HZBANK\",\"projectName\":\"Track\",\"description\":\"服务工程\",\"status\":\"0\",\"repository\":\"\"}]}";
//        aStatic.when(() -> HuToolHttpUtil.sentGet("巨象平台", "http://*************:8000/api/v1/subsysProject","Cookie",null)).thenReturn(s);
//        String s1 = "[{\"id\":\"8\",\"subsysCode\":\"SUBSYS_AFLOAN_FERACK\",\"projectName\":\"FunTrack\",\"description\":\"资金跟踪\",\"status\":\"0\",\"repository\":\"\"},{\"id\":\"9\",\"subsysCode\":\"SUBSYS_AFLOAN_FERACK\",\"projectName\":\"Fun\",\"description\":\"跟踪监测\",\"status\":\"0\",\"repository\":\"\"},{\"id\":\"1\",\"subsysCode\":\"HZBANK\",\"projectName\":\"Track\",\"description\":\"服务工程\",\"status\":\"0\",\"repository\":\"\"}]";
//        aStatic.when(() -> HuToolHttpUtil.getStrFromData(s, "data")).thenReturn(s1);
//        BaseResult<List<SubSystemVO>> result = elephantController.listAllSubSystem("HZBANK");
//        System.out.println(JSON.toJSONString(result));
//    }
//}