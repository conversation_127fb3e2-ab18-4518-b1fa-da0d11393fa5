package cn.harmonycloud.development.outbound.util;

import cn.harmonycloud.development.execption.thgn.GitException;
import cn.harmonycloud.development.service.trinasolar.GitlabService;
import cn.harmonycloud.enums.ExceptionCode;
import org.gitlab4j.api.GitLabApi;
import org.gitlab4j.api.GitLabApiException;
import org.gitlab4j.api.ProjectApi;
import org.gitlab4j.api.models.Project;
import org.gitlab4j.api.models.Visibility;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

/**
 * GitlabUtil单元测试类
 *
 * <AUTHOR>
 */
@RunWith(MockitoJUnitRunner.class)
public class GitlabServiceTest {

    @InjectMocks
    private GitlabService gitlabService;

    @Mock
    private GitLabApi mockGitLabApi;

    @Mock
    private ProjectApi mockProjectApi;

    @Mock
    private Project mockProject;

    /**
     * 测试前的初始化方法
     * 创建GitlabUtil实例并设置必要的配置参数
     */
    @Before
    public void setUp() {
        // 创建被测试的GitlabUtil实例
        gitlabService = new GitlabService();

        // 使用反射设置私有字段的测试值，模拟Spring配置注入
        ReflectionTestUtils.setField(gitlabService, "gitUrl", "https://code.trinasolar.com");
        ReflectionTestUtils.setField(gitlabService, "gitToken", "**************************");
    }

    /**
     * 测试createGitlabRepository方法的成功场景（使用分离参数）
     *
     * 测试目标：验证在正常情况下能够成功创建GitLab仓库
     * 测试步骤：
     * 1. 准备测试数据（分离的参数）
     * 2. Mock GitLabApi和ProjectApi的行为
     * 3. 调用被测试方法
     * 4. 验证返回结果和方法调用
     *
     * @throws GitLabApiException 如果GitLab API调用失败
     */
    @Test
    public void testCreateGitlabRepository_WithSeparateParams_Success() throws GitLabApiException {
        // Given - 准备测试数据
        String repoBaseUrl = "https://code.trinasolar.com";
        String businessName = "itid";
        String systemSimpleName = "test187";
        String programEnName = "test-prog-1867";

        // 使用spy来部分mock GitlabService，保留真实的createGitlabRepository方法逻辑
        GitlabService spyGitlabService = spy(gitlabService);
        // Mock getGitLabApi方法返回我们的mock对象
        doReturn(mockGitLabApi).when(spyGitlabService).getGitLabApi();

        // Mock GitLabApi的行为：返回mock的ProjectApi
        when(mockGitLabApi.getProjectApi()).thenReturn(mockProjectApi);

        // 创建ArgumentCaptor来捕获传递给createProject的Project对象
        ArgumentCaptor<Project> projectCaptor = ArgumentCaptor.forClass(Project.class);
        when(mockProjectApi.createProject(projectCaptor.capture())).thenReturn(mockProject);

        // When - 执行被测试的方法
        Project result = spyGitlabService.createGitlabRepository(repoBaseUrl, businessName, systemSimpleName, programEnName);

        // Then - 验证结果
        assertNotNull("返回的Project对象不应该为null", result);
        assertEquals("返回的Project对象应该与mock对象相同", mockProject, result);

        // 验证传递给createProject的Project对象的属性
        Project capturedProject = projectCaptor.getValue();
        assertEquals("项目名称应该正确", programEnName, capturedProject.getName());
        assertEquals("项目路径应该正确", programEnName, capturedProject.getPath());
       // assertEquals("命名空间路径应该正确", businessName + "/" + systemSimpleName, capturedProject.getNamespacePath());
        assertEquals("可见性应该是私有", Visibility.PRIVATE, capturedProject.getVisibility());
        assertFalse("AutoDevops应该被禁用", capturedProject.getAutoDevopsEnabled());
        assertTrue("应该初始化README", capturedProject.getInitializeWithReadme());

        // 验证ProjectApi的createProject方法被正确调用
        verify(mockProjectApi).createProject(any(Project.class));
    }

    /**
     * 测试createGitlabRepository方法的成功场景（使用完整URL）
     *
     * 测试目标：验证从完整URL解析参数并成功创建GitLab仓库
     * 测试步骤：
     * 1. 准备测试数据（完整URL）
     * 2. Mock GitLabApi和ProjectApi的行为
     * 3. 调用被测试方法
     * 4. 验证返回结果和方法调用
     *
     * @throws GitLabApiException 如果GitLab API调用失败
     */
    @Test
    public void testCreateGitlabRepository_WithFullUrl_Success() throws GitLabApiException {
        // Given - 准备测试数据
        String repoUrl = "https://code.trinasolar.com/itid/test186/test-prog-1866.git";

        // 使用spy来部分mock GitlabService
        GitlabService spyGitlabService = spy(gitlabService);
        // Mock getGitLabApi方法返回我们的mock对象
        doReturn(mockGitLabApi).when(spyGitlabService).getGitLabApi();

        // Mock GitLabApi的行为：返回mock的ProjectApi
        when(mockGitLabApi.getProjectApi()).thenReturn(mockProjectApi);

        // 创建ArgumentCaptor来捕获传递给createProject的Project对象
        ArgumentCaptor<Project> projectCaptor = ArgumentCaptor.forClass(Project.class);
        when(mockProjectApi.createProject(projectCaptor.capture())).thenReturn(mockProject);

        // When - 执行被测试的方法
        Project result = spyGitlabService.createGitlabRepository(repoUrl);

        // Then - 验证结果
        assertNotNull("返回的Project对象不应该为null", result);
        assertEquals("返回的Project对象应该与mock对象相同", mockProject, result);

        // 验证传递给createProject的Project对象的属性
        Project capturedProject = projectCaptor.getValue();
        assertEquals("项目名称应该正确", "test-prog-1866", capturedProject.getName());
        assertEquals("项目路径应该正确", "test-prog-1866", capturedProject.getPath());
       // assertEquals("命名空间路径应该正确", "itid/test186", capturedProject.getNamespacePath());
        assertEquals("可见性应该是私有", Visibility.PRIVATE, capturedProject.getVisibility());

        // 验证ProjectApi的createProject方法被正确调用
        verify(mockProjectApi).createProject(any(Project.class));
    }

    /**
     * 测试createGitlabRepository方法的异常处理场景（使用分离参数）
     *
     * 测试目标：验证当GitLab API调用失败时，方法能够正确处理异常并转换为业务异常
     * 测试步骤：
     * 1. 准备测试数据和异常对象
     * 2. Mock GitLabApi抛出GitLabApiException
     * 3. 调用被测试方法
     * 4. 验证抛出的GitException包含正确的错误代码和消息
     *
     * @throws GitLabApiException 如果GitLab API调用失败
     */
    @Test
    public void testCreateGitlabRepository_WithSeparateParams_GitLabApiException() throws GitLabApiException {
        // Given - 准备测试数据和异常
        String repoBaseUrl = "https://code.trinasolar.com";
        String businessName = "itid";
        String systemSimpleName = "test186";
        String programEnName = "test-prog-1866";
        String errorMessage = "API调用失败";
        GitLabApiException gitLabApiException = new GitLabApiException(errorMessage);

        // 使用spy来部分mock GitlabService
        GitlabService spyGitlabService = spy(gitlabService);
        doReturn(mockGitLabApi).when(spyGitlabService).getGitLabApi();

        // Mock GitLabApi的行为：ProjectApi的createProject方法抛出异常
        when(mockGitLabApi.getProjectApi()).thenReturn(mockProjectApi);
        when(mockProjectApi.createProject(any(Project.class))).thenThrow(gitLabApiException);

        // When & Then - 执行方法并验证异常
        try {
            spyGitlabService.createGitlabRepository(repoBaseUrl, businessName, systemSimpleName, programEnName);
            fail("应该抛出GitException，但方法正常执行完成");
        } catch (GitException exception) {
            // 验证异常的错误代码
            assertEquals("异常代码应该是CREATE_REPOSITORY_FAIL",
                        ExceptionCode.CREATE_REPOSITORY_FAIL, exception.getExceptionCode());
            // 验证异常消息包含预期的错误信息
            assertTrue("异常消息应该包含'create repo fail'",
                      exception.getMessage().contains("create repo fail"));
            assertTrue("异常消息应该包含原始错误信息",
                      exception.getMessage().contains(errorMessage));
        }

        // 验证ProjectApi的createProject方法被调用
        verify(mockProjectApi).createProject(any(Project.class));
    }

    /**
     * 测试createGitlabRepository方法的异常处理场景（使用完整URL）
     *
     * 测试目标：验证当GitLab API调用失败时，方法能够正确处理异常并转换为业务异常
     * 测试步骤：
     * 1. 准备测试数据和异常对象
     * 2. Mock GitLabApi抛出GitLabApiException
     * 3. 调用被测试方法
     * 4. 验证抛出的GitException包含正确的错误代码和消息
     *
     * @throws GitLabApiException 如果GitLab API调用失败
     */
    @Test
    public void testCreateGitlabRepository_WithFullUrl_GitLabApiException() throws GitLabApiException {
        // Given - 准备测试数据和异常
        String repoUrl = "https://code.trinasolar.com/itid/test186/test-prog-1866.git";
        String errorMessage = "API调用失败";
        GitLabApiException gitLabApiException = new GitLabApiException(errorMessage);

        // 使用spy来部分mock GitlabService
        GitlabService spyGitlabService = spy(gitlabService);
        doReturn(mockGitLabApi).when(spyGitlabService).getGitLabApi();

        // Mock GitLabApi的行为：ProjectApi的createProject方法抛出异常
        when(mockGitLabApi.getProjectApi()).thenReturn(mockProjectApi);
        when(mockProjectApi.createProject(any(Project.class))).thenThrow(gitLabApiException);

        // When & Then - 执行方法并验证异常
        try {
            spyGitlabService.createGitlabRepository(repoUrl);
            fail("应该抛出GitException，但方法正常执行完成");
        } catch (GitException exception) {
            // 验证异常的错误代码
            assertEquals("异常代码应该是CREATE_REPOSITORY_FAIL",
                        ExceptionCode.CREATE_REPOSITORY_FAIL, exception.getExceptionCode());
            // 验证异常消息包含预期的错误信息
            assertTrue("异常消息应该包含'create repo fail'",
                      exception.getMessage().contains("create repo fail"));
            assertTrue("异常消息应该包含原始错误信息",
                      exception.getMessage().contains(errorMessage));
        }

        // 验证ProjectApi的createProject方法被调用
        verify(mockProjectApi).createProject(any(Project.class));
    }

    /**
     * 测试createGitlabRepository方法处理无效URL格式的场景
     *
     * 测试目标：验证当传入无效格式的URL时，方法能够正确处理并抛出适当的异常
     * 测试步骤：
     * 1. 准备无效格式的URL
     * 2. 调用被测试方法
     * 3. 验证抛出的GitException包含正确的错误代码
     */
    @Test
    public void testCreateGitlabRepository_InvalidUrlFormat() {
        // Given - 准备无效格式的URL（路径部分不足3段）
        String invalidRepoUrl = "https://code.trinasolar.com/invalid.git";

        // 使用spy来部分mock GitlabService
        GitlabService spyGitlabService = spy(gitlabService);

        // When & Then - 执行方法并验证异常
        try {
            spyGitlabService.createGitlabRepository(invalidRepoUrl);
            fail("应该抛出GitException，但方法正常执行完成");
        } catch (GitException exception) {
            // 验证异常的错误代码
            assertEquals("异常代码应该是CREATE_REPOSITORY_FAIL",
                        ExceptionCode.CREATE_REPOSITORY_FAIL, exception.getExceptionCode());
            // 验证异常消息包含预期的错误信息
            assertTrue("异常消息应该包含'create repo from url fail'",
                      exception.getMessage().contains("create repo from url fail"));
        }
    }

    /**
     * 测试createGitlabRepository方法处理null参数的场景
     *
     * 测试目标：验证当传入null参数时，方法能够正确处理并抛出适当的异常
     */
    @Test
    public void testCreateGitlabRepository_NullParams() {
        // Given - 准备null参数
        String repoBaseUrl = null;
        String businessName = null;
        String systemSimpleName = null;
        String programEnName = null;

        // 使用spy来部分mock GitlabService
        GitlabService spyGitlabService = spy(gitlabService);

        // When & Then - 执行方法并验证异常
        try {
            spyGitlabService.createGitlabRepository(repoBaseUrl, businessName, systemSimpleName, programEnName);
            fail("应该抛出异常，但方法正常执行完成");
        } catch (Exception exception) {
            // 验证抛出了异常（可能是NullPointerException或GitException）
            assertNotNull("应该抛出异常", exception);
        }
    }

    /**
     * 测试createGitlabRepository方法处理API返回null的场景
     *
     * 测试目标：验证当GitLab API返回null时，方法能够正确处理并返回null
     * 测试步骤：
     * 1. 准备有效的参数
     * 2. Mock GitLabApi的createProject方法返回null
     * 3. 调用被测试方法
     * 4. 验证方法返回null且没有抛出异常
     *
     * @throws GitLabApiException 如果GitLab API调用失败
     */
    @Test
    public void testCreateGitlabRepository_ProjectApiReturnsNull() throws GitLabApiException {
        // Given - 准备测试数据
        String repoBaseUrl = "https://code.trinasolar.com";
        String businessName = "itid";
        String systemSimpleName = "test186";
        String programEnName = "test-prog-1866";

        // 使用spy来部分mock GitlabService
        GitlabService spyGitlabService = spy(gitlabService);
        doReturn(mockGitLabApi).when(spyGitlabService).getGitLabApi();

        // Mock GitLabApi的行为：createProject方法返回null
        when(mockGitLabApi.getProjectApi()).thenReturn(mockProjectApi);
        when(mockProjectApi.createProject(any(Project.class))).thenReturn(null);

        // When - 执行被测试的方法
        Project result = spyGitlabService.createGitlabRepository(repoBaseUrl, businessName, systemSimpleName, programEnName);

        // Then - 验证结果
        assertNull("当API返回null时，方法也应该返回null", result);
        // 验证ProjectApi的createProject方法被正确调用
        verify(mockProjectApi).createProject(any(Project.class));
    }

    /**
     * 测试URL解析功能
     *
     * 测试目标：验证URL解析逻辑能够正确提取业务名称、系统简称和项目名称
     */
    @Test
    public void testCreateGitlabRepository_UrlParsing() throws GitLabApiException {
        // Given - 准备不同格式的URL
        String[] testUrls = {
            "https://code.trinasolar.com/business/system/project.git",
            "http://gitlab.example.com:8080/org/team/app.git",
            "https://gitlab.com/group/subgroup/repo"
        };

        String[] expectedBusinessNames = {"business", "org", "group"};
        String[] expectedSystemNames = {"system", "team", "subgroup"};
        String[] expectedProjectNames = {"project", "app", "repo"};

        // 使用spy来部分mock GitlabService
        GitlabService spyGitlabService = spy(gitlabService);
        doReturn(mockGitLabApi).when(spyGitlabService).getGitLabApi();
        when(mockGitLabApi.getProjectApi()).thenReturn(mockProjectApi);

        ArgumentCaptor<Project> projectCaptor = ArgumentCaptor.forClass(Project.class);
        when(mockProjectApi.createProject(projectCaptor.capture())).thenReturn(mockProject);

//        for (int i = 0; i < testUrls.length; i++) {
//            // When - 执行被测试的方法
//            spyGitlabService.createGitlabRepository(testUrls[i]);
//
//            // Then - 验证URL解析结果
//            Project capturedProject = projectCaptor.getAllValues().get(i);
//            assertEquals("项目名称解析错误", expectedProjectNames[i], capturedProject.getName());
//            assertEquals("项目路径解析错误", expectedProjectNames[i], capturedProject.getPath());
//            assertEquals("命名空间路径解析错误",
//                        expectedBusinessNames[i] + "/" + expectedSystemNames[i],
//                        capturedProject.getNamespacePath());
//        }
    }

    /**
     * 测试项目参数设置的完整性
     *
     * 测试目标：验证创建的Project对象包含所有必要的参数设置
     */
    @Test
    public void testCreateGitlabRepository_ProjectParametersCompleteness() throws GitLabApiException {
        // Given - 准备测试数据
        String repoBaseUrl = "https://code.trinasolar.com";
        String businessName = "test-business";
        String systemSimpleName = "test-system";
        String programEnName = "test-project";

        // 使用spy来部分mock GitlabService
        GitlabService spyGitlabService = spy(gitlabService);
        doReturn(mockGitLabApi).when(spyGitlabService).getGitLabApi();
        when(mockGitLabApi.getProjectApi()).thenReturn(mockProjectApi);

        ArgumentCaptor<Project> projectCaptor = ArgumentCaptor.forClass(Project.class);
        when(mockProjectApi.createProject(projectCaptor.capture())).thenReturn(mockProject);

        // When - 执行被测试的方法
        spyGitlabService.createGitlabRepository(repoBaseUrl, businessName, systemSimpleName, programEnName);

        // Then - 验证Project对象的所有参数
        Project capturedProject = projectCaptor.getValue();

        // 验证基本属性
        assertEquals("项目名称设置错误", programEnName, capturedProject.getName());
        assertEquals("项目路径设置错误", programEnName, capturedProject.getPath());
       // assertEquals("命名空间路径设置错误", businessName + "/" + systemSimpleName, capturedProject.getNamespacePath());

        // 验证安全和配置属性
        assertEquals("可见性应该设置为私有", Visibility.PRIVATE, capturedProject.getVisibility());
        assertFalse("AutoDevops应该被禁用", capturedProject.getAutoDevopsEnabled());
        assertTrue("应该初始化README文件", capturedProject.getInitializeWithReadme());
    }

    /**
     * 测试带端口号的URL解析
     *
     * 测试目标：验证包含端口号的URL能够正确解析
     */
    @Test
    public void testCreateGitlabRepository_UrlWithPort() throws GitLabApiException {
        // Given - 准备带端口号的URL
        String repoUrl = "https://gitlab.example.com:8443/business/system/project.git";

        // 使用spy来部分mock GitlabService
        GitlabService spyGitlabService = spy(gitlabService);
        doReturn(mockGitLabApi).when(spyGitlabService).getGitLabApi();
        when(mockGitLabApi.getProjectApi()).thenReturn(mockProjectApi);

        ArgumentCaptor<Project> projectCaptor = ArgumentCaptor.forClass(Project.class);
        when(mockProjectApi.createProject(projectCaptor.capture())).thenReturn(mockProject);

        // When - 执行被测试的方法
        spyGitlabService.createGitlabRepository(repoUrl);

        // Then - 验证解析结果
        Project capturedProject = projectCaptor.getValue();
        assertEquals("项目名称解析错误", "project", capturedProject.getName());
      //  assertEquals("命名空间路径解析错误", "business/system", capturedProject.getNamespacePath());
    }

    /**
     * 测试getGitLabApi方法的基本功能
     *
     * 测试目标：验证getGitLabApi方法能够正确创建并返回GitLabApi实例
     * 测试步骤：
     * 1. 调用getGitLabApi方法
     * 2. 验证返回的GitLabApi实例不为null
     *
     * 注意：这个测试会创建真实的GitLabApi实例，需要有效的URL配置
     */
    @Test
    public void testGetGitLabApi() {
        // When - 执行被测试的方法
        GitLabApi result = gitlabService.getGitLabApi();

        // Then - 验证结果
        assertNotNull("getGitLabApi方法应该返回非null的GitLabApi实例", result);

        // 验证GitLabApi实例的基本属性
        // 注意：由于GitLabApi是第三方库，我们主要验证实例创建成功
    }
}
