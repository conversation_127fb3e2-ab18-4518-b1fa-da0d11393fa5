package cn.harmonycloud.development.outbound.util;

import cn.harmonycloud.development.execption.thgn.GitException;
import cn.harmonycloud.development.service.trinasolar.GitlabService;
import cn.harmonycloud.enums.ExceptionCode;
import org.gitlab4j.api.GitLabApi;
import org.gitlab4j.api.GitLabApiException;
import org.gitlab4j.api.ProjectApi;
import org.gitlab4j.api.models.Project;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

/**
 * GitlabUtil简化版单元测试类
 * 专注于测试createGitlabRepository方法，避免真实的GitLabApi调用
 * 
 * <AUTHOR>
 */
@RunWith(MockitoJUnitRunner.class)
public class GitlabServiceSimpleTest {

    @Mock
    private GitLabApi mockGitLabApi;

    @Mock
    private ProjectApi mockProjectApi;

    @Mock
    private Project mockProject;

    private GitlabService gitlabService;

    /**
     * 测试前的初始化方法
     * 创建GitlabUtil实例的spy对象，完全mock掉getGitLabApi方法
     */
    @Before
    public void setUp() {
        // 创建GitlabUtil的spy对象
        gitlabService = spy(new GitlabService());
        
        // 完全mock掉getGitLabApi方法，避免真实的GitLabApi创建
        doReturn(mockGitLabApi).when(gitlabService).getGitLabApi();
    }

    /**
     * 测试createGitlabRepository方法的成功场景
     * 
     * 测试目标：验证在正常情况下能够成功创建GitLab仓库
     * 测试步骤：
     * 1. 准备测试数据（仓库URL）
     * 2. Mock GitLabApi和ProjectApi的行为
     * 3. 调用被测试方法
     * 4. 验证返回结果和方法调用
     * 
     * @throws GitLabApiException 如果GitLab API调用失败
     */
    @Test
    public void testCreateGitlabRepository_Success() throws GitLabApiException {
        // Given - 准备测试数据
        String repoUrl = "test-repo";

        // Mock GitLabApi的行为：返回mock的ProjectApi
        when(mockGitLabApi.getProjectApi()).thenReturn(mockProjectApi);
        // Mock ProjectApi的createProject方法：返回mock的Project对象
        when(mockProjectApi.createProject(repoUrl)).thenReturn(mockProject);

        // When - 执行被测试的方法
        Project result = gitlabService.createGitlabRepository(repoUrl);

        // Then - 验证结果
        assertNotNull("返回的Project对象不应该为null", result);
        assertEquals("返回的Project对象应该与mock对象相同", mockProject, result);
        
        // 验证方法调用
        verify(gitlabService).getGitLabApi();
        verify(mockGitLabApi).getProjectApi();
        verify(mockProjectApi).createProject(repoUrl);
    }

    /**
     * 测试createGitlabRepository方法的异常处理场景
     * 
     * 测试目标：验证当GitLab API调用失败时，方法能够正确处理异常并转换为业务异常
     * 测试步骤：
     * 1. 准备测试数据和异常对象
     * 2. Mock GitLabApi抛出GitLabApiException
     * 3. 调用被测试方法
     * 4. 验证抛出的GitException包含正确的错误代码和消息
     * 
     * @throws GitLabApiException 如果GitLab API调用失败
     */
    @Test
    public void testCreateGitlabRepository_GitLabApiException() throws GitLabApiException {
        // Given - 准备测试数据和异常
        String repoUrl = "test-repo";
        String errorMessage = "API调用失败";
        GitLabApiException gitLabApiException = new GitLabApiException(errorMessage);

        // Mock GitLabApi的行为：ProjectApi的createProject方法抛出异常
        when(mockGitLabApi.getProjectApi()).thenReturn(mockProjectApi);
        when(mockProjectApi.createProject(repoUrl)).thenThrow(gitLabApiException);

        // When & Then - 执行方法并验证异常
        try {
            gitlabService.createGitlabRepository(repoUrl);
            fail("应该抛出GitException，但方法正常执行完成");
        } catch (GitException exception) {
            // 验证异常的错误代码
            assertEquals("异常代码应该是CREATE_REPOSITORY_FAIL", 
                        ExceptionCode.CREATE_REPOSITORY_FAIL, exception.getExceptionCode());
            // 验证异常消息包含预期的错误信息
            assertTrue("异常消息应该包含'仓库创建失败'", 
                      exception.getMessage().contains("仓库创建失败"));
            assertTrue("异常消息应该包含原始错误信息", 
                      exception.getMessage().contains(errorMessage));
        }

        // 验证方法调用
        verify(gitlabService).getGitLabApi();
        verify(mockGitLabApi).getProjectApi();
        verify(mockProjectApi).createProject(repoUrl);
    }

    /**
     * 测试createGitlabRepository方法处理API返回null的场景
     * 
     * 测试目标：验证当GitLab API返回null时，方法能够正确处理并返回null
     * 测试步骤：
     * 1. 准备有效的仓库URL
     * 2. Mock GitLabApi的createProject方法返回null
     * 3. 调用被测试方法
     * 4. 验证方法返回null且没有抛出异常
     * 
     * @throws GitLabApiException 如果GitLab API调用失败
     */
    @Test
    public void testCreateGitlabRepository_ProjectApiReturnsNull() throws GitLabApiException {
        // Given - 准备测试数据
        String repoUrl = "test-repo";

        // Mock GitLabApi的行为：createProject方法返回null
        when(mockGitLabApi.getProjectApi()).thenReturn(mockProjectApi);
        when(mockProjectApi.createProject(repoUrl)).thenReturn(null);

        // When - 执行被测试的方法
        Project result = gitlabService.createGitlabRepository(repoUrl);

        // Then - 验证结果
        assertNull("当API返回null时，方法也应该返回null", result);
        
        // 验证方法调用
        verify(gitlabService).getGitLabApi();
        verify(mockGitLabApi).getProjectApi();
        verify(mockProjectApi).createProject(repoUrl);
    }

    /**
     * 测试createGitlabRepository方法处理null参数的场景
     * 
     * 测试目标：验证当传入null仓库URL时，方法能够正确处理并抛出适当的异常
     * 测试步骤：
     * 1. 准备null参数
     * 2. Mock GitLabApi在接收到null参数时抛出异常
     * 3. 调用被测试方法
     * 4. 验证抛出的GitException包含正确的错误代码
     * 
     * @throws GitLabApiException 如果GitLab API调用失败
     */
    @Test
    public void testCreateGitlabRepository_NullRepoUrl() throws GitLabApiException {
        // Given - 准备null参数
        String repoUrl = null;

        // Mock GitLabApi的行为：当传入null参数时抛出异常
        when(mockGitLabApi.getProjectApi()).thenReturn(mockProjectApi);
        when(mockProjectApi.createProject(repoUrl))
            .thenThrow(new GitLabApiException("Invalid repository URL"));

        // When & Then - 执行方法并验证异常
        try {
            gitlabService.createGitlabRepository(repoUrl);
            fail("应该抛出GitException，但方法正常执行完成");
        } catch (GitException exception) {
            // 验证异常的错误代码
            assertEquals("异常代码应该是CREATE_REPOSITORY_FAIL", 
                        ExceptionCode.CREATE_REPOSITORY_FAIL, exception.getExceptionCode());
            // 验证异常消息包含预期的错误信息
            assertTrue("异常消息应该包含'仓库创建失败'", 
                      exception.getMessage().contains("仓库创建失败"));
        }

        // 验证方法调用
        verify(gitlabService).getGitLabApi();
        verify(mockGitLabApi).getProjectApi();
        verify(mockProjectApi).createProject(repoUrl);
    }
}
