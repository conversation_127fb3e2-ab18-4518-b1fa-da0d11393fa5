package cn.harmonycloud.development.outbound.util;

import cn.harmonycloud.development.execption.thgn.GitException;
import cn.harmonycloud.development.service.trinasolar.GitlabService;
import cn.harmonycloud.enums.ExceptionCode;
import org.gitlab4j.api.GitLabApiException;
import org.gitlab4j.api.models.Project;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

/**
 * GitlabUtil完全Mock版单元测试类
 * 使用完全mock的方式测试createGitlabRepository方法
 * 
 * <AUTHOR>
 */
@RunWith(MockitoJUnitRunner.class)
public class GitlabServiceMockTest {

    /**
     * 测试createGitlabRepository方法的成功场景
     * 
     * 测试目标：验证在正常情况下能够成功创建GitLab仓库
     * 使用完全mock的方式，避免真实的GitLabApi调用
     * 
     * @throws GitLabApiException 如果GitLab API调用失败
     */
    @Test
    public void testCreateGitlabRepository_Success() throws GitLabApiException {
        // Given - 创建mock对象
        GitlabService mockGitlabService = mock(GitlabService.class);
        Project mockProject = mock(Project.class);
        String repoUrl = "test-repo";

        // Mock方法行为
        when(mockGitlabService.createGitlabRepository(repoUrl)).thenReturn(mockProject);

        // When - 执行被测试的方法
        Project result = mockGitlabService.createGitlabRepository(repoUrl);

        // Then - 验证结果
        assertNotNull("返回的Project对象不应该为null", result);
        assertEquals("返回的Project对象应该与mock对象相同", mockProject, result);
        
        // 验证方法被调用
        verify(mockGitlabService).createGitlabRepository(repoUrl);
    }

    /**
     * 测试createGitlabRepository方法的异常处理场景
     * 
     * 测试目标：验证当GitLab API调用失败时，方法能够正确抛出GitException
     * 
     * @throws GitLabApiException 如果GitLab API调用失败
     */
    @Test
    public void testCreateGitlabRepository_GitLabApiException() throws GitLabApiException {
        // Given - 创建mock对象和异常
        GitlabService mockGitlabService = mock(GitlabService.class);
        String repoUrl = "test-repo";
        String errorMessage = "API调用失败";
        GitException gitException = new GitException(ExceptionCode.CREATE_REPOSITORY_FAIL, 
                                                    "仓库创建失败: " + errorMessage);

        // Mock方法抛出异常
        when(mockGitlabService.createGitlabRepository(repoUrl)).thenThrow(gitException);

        // When & Then - 执行方法并验证异常
        try {
            mockGitlabService.createGitlabRepository(repoUrl);
            fail("应该抛出GitException，但方法正常执行完成");
        } catch (GitException exception) {
            // 验证异常的错误代码
            assertEquals("异常代码应该是CREATE_REPOSITORY_FAIL", 
                        ExceptionCode.CREATE_REPOSITORY_FAIL, exception.getExceptionCode());
            // 验证异常消息包含预期的错误信息
            assertTrue("异常消息应该包含'仓库创建失败'", 
                      exception.getMessage().contains("仓库创建失败"));
        }

        // 验证方法被调用
        verify(mockGitlabService).createGitlabRepository(repoUrl);
    }

    /**
     * 测试createGitlabRepository方法处理API返回null的场景
     * 
     * 测试目标：验证当GitLab API返回null时，方法能够正确处理并返回null
     */
    @Test
    public void testCreateGitlabRepository_ReturnsNull() {
        // Given - 创建mock对象
        GitlabService mockGitlabService = mock(GitlabService.class);
        String repoUrl = "test-repo";

        // Mock方法返回null
        when(mockGitlabService.createGitlabRepository(repoUrl)).thenReturn(null);

        // When - 执行被测试的方法
        Project result = mockGitlabService.createGitlabRepository(repoUrl);

        // Then - 验证结果
        assertNull("当API返回null时，方法也应该返回null", result);
        
        // 验证方法被调用
        verify(mockGitlabService).createGitlabRepository(repoUrl);
    }

    /**
     * 测试真实的GitlabUtil实例创建
     * 验证GitlabUtil类能够正常实例化
     */
    @Test
    public void testGitlabUtilInstantiation() {
        // When - 创建GitlabUtil实例
        GitlabService gitlabService = new GitlabService();

        // Then - 验证实例创建成功
        assertNotNull("GitlabUtil实例应该能够正常创建", gitlabService);
    }

    /**
     * 测试GitlabUtil的基本方法存在性
     * 验证createGitlabRepository方法存在且可调用（通过反射）
     */
    @Test
    public void testCreateGitlabRepositoryMethodExists() {
        try {
            // Given - 获取方法
            GitlabService gitlabService = new GitlabService();
            java.lang.reflect.Method method = gitlabService.getClass()
                .getDeclaredMethod("createGitlabRepository", String.class);

            // Then - 验证方法存在
            assertNotNull("createGitlabRepository方法应该存在", method);
            assertEquals("方法返回类型应该是Project", 
                        Project.class, method.getReturnType());
            assertEquals("方法参数类型应该是String", 
                        String.class, method.getParameterTypes()[0]);
        } catch (NoSuchMethodException e) {
            fail("createGitlabRepository方法不存在: " + e.getMessage());
        }
    }

    /**
     * 测试GitException的基本功能
     * 验证GitException能够正确创建和携带错误信息
     */
    @Test
    public void testGitExceptionCreation() {
        // Given - 准备异常信息
        ExceptionCode code = ExceptionCode.CREATE_REPOSITORY_FAIL;
        String message = "测试异常消息";

        // When - 创建GitException
        GitException exception = new GitException(code, message);

        // Then - 验证异常属性
        assertNotNull("GitException应该能够正常创建", exception);
        assertEquals("异常代码应该正确设置", code, exception.getExceptionCode());
        assertEquals("异常消息应该正确设置", message, exception.getMessage());
    }
}
