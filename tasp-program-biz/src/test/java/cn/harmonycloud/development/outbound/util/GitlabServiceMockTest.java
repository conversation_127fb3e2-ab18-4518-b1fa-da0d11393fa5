package cn.harmonycloud.development.outbound.util;

import cn.harmonycloud.development.execption.thgn.GitException;
import cn.harmonycloud.development.service.trinasolar.GitlabService;
import cn.harmonycloud.enums.ExceptionCode;
import org.gitlab4j.api.GitLabApiException;
import org.gitlab4j.api.models.Project;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

/**
 * GitlabUtil完全Mock版单元测试类
 * 使用完全mock的方式测试createGitlabRepository方法
 * 
 * <AUTHOR>
 */
@RunWith(MockitoJUnitRunner.class)
public class GitlabServiceMockTest {

    /**
     * 测试createGitlabRepository方法的成功场景（使用分离参数）
     *
     * 测试目标：验证在正常情况下能够成功创建GitLab仓库
     * 使用完全mock的方式，避免真实的GitLabApi调用
     *
     * @throws GitLabApiException 如果GitLab API调用失败
     */
    @Test
    public void testCreateGitlabRepository_WithSeparateParams_Success() throws GitLabApiException {
        // Given - 创建mock对象
        GitlabService mockGitlabService = mock(GitlabService.class);
        Project mockProject = mock(Project.class);
        String repoBaseUrl = "https://code.trinasolar.com";
        String businessName = "itid";
        String systemSimpleName = "test186";
        String programEnName = "test-prog-1866";

        // Mock方法行为
        when(mockGitlabService.createGitlabRepository(repoBaseUrl, businessName, systemSimpleName, programEnName))
            .thenReturn(mockProject);

        // When - 执行被测试的方法
        Project result = mockGitlabService.createGitlabRepository(repoBaseUrl, businessName, systemSimpleName, programEnName);

        // Then - 验证结果
        assertNotNull("返回的Project对象不应该为null", result);
        assertEquals("返回的Project对象应该与mock对象相同", mockProject, result);

        // 验证方法被调用
        verify(mockGitlabService).createGitlabRepository(repoBaseUrl, businessName, systemSimpleName, programEnName);
    }

    /**
     * 测试createGitlabRepository方法的成功场景（使用完整URL）
     *
     * 测试目标：验证在正常情况下能够成功创建GitLab仓库
     * 使用完全mock的方式，避免真实的GitLabApi调用
     *
     * @throws GitLabApiException 如果GitLab API调用失败
     */
    @Test
    public void testCreateGitlabRepository_WithFullUrl_Success() throws GitLabApiException {
        // Given - 创建mock对象
        GitlabService mockGitlabService = mock(GitlabService.class);
        Project mockProject = mock(Project.class);
        String repoUrl = "https://code.trinasolar.com/itid/test186/test-prog-1866.git";

        // Mock方法行为
        when(mockGitlabService.createGitlabRepository(repoUrl)).thenReturn(mockProject);

        // When - 执行被测试的方法
        Project result = mockGitlabService.createGitlabRepository(repoUrl);

        // Then - 验证结果
        assertNotNull("返回的Project对象不应该为null", result);
        assertEquals("返回的Project对象应该与mock对象相同", mockProject, result);

        // 验证方法被调用
        verify(mockGitlabService).createGitlabRepository(repoUrl);
    }

    /**
     * 测试createGitlabRepository方法的异常处理场景（使用分离参数）
     *
     * 测试目标：验证当GitLab API调用失败时，方法能够正确抛出GitException
     *
     * @throws GitLabApiException 如果GitLab API调用失败
     */
    @Test
    public void testCreateGitlabRepository_WithSeparateParams_GitLabApiException() throws GitLabApiException {
        // Given - 创建mock对象和异常
        GitlabService mockGitlabService = mock(GitlabService.class);
        String repoBaseUrl = "https://code.trinasolar.com";
        String businessName = "itid";
        String systemSimpleName = "test186";
        String programEnName = "test-prog-1866";
        String errorMessage = "API调用失败";
        GitException gitException = new GitException(ExceptionCode.CREATE_REPOSITORY_FAIL,
                                                    "create repo fail: " + errorMessage);

        // Mock方法抛出异常
        when(mockGitlabService.createGitlabRepository(repoBaseUrl, businessName, systemSimpleName, programEnName))
            .thenThrow(gitException);

        // When & Then - 执行方法并验证异常
        try {
            mockGitlabService.createGitlabRepository(repoBaseUrl, businessName, systemSimpleName, programEnName);
            fail("应该抛出GitException，但方法正常执行完成");
        } catch (GitException exception) {
            // 验证异常的错误代码
            assertEquals("异常代码应该是CREATE_REPOSITORY_FAIL",
                        ExceptionCode.CREATE_REPOSITORY_FAIL, exception.getExceptionCode());
            // 验证异常消息包含预期的错误信息
            assertTrue("异常消息应该包含'create repo fail'",
                      exception.getMessage().contains("create repo fail"));
        }

        // 验证方法被调用
        verify(mockGitlabService).createGitlabRepository(repoBaseUrl, businessName, systemSimpleName, programEnName);
    }

    /**
     * 测试createGitlabRepository方法的异常处理场景（使用完整URL）
     *
     * 测试目标：验证当GitLab API调用失败时，方法能够正确抛出GitException
     *
     * @throws GitLabApiException 如果GitLab API调用失败
     */
    @Test
    public void testCreateGitlabRepository_WithFullUrl_GitLabApiException() throws GitLabApiException {
        // Given - 创建mock对象和异常
        GitlabService mockGitlabService = mock(GitlabService.class);
        String repoUrl = "https://code.trinasolar.com/itid/test186/test-prog-1866.git";
        String errorMessage = "API调用失败";
        GitException gitException = new GitException(ExceptionCode.CREATE_REPOSITORY_FAIL,
                                                    "create repo from url fail: " + errorMessage);

        // Mock方法抛出异常
        when(mockGitlabService.createGitlabRepository(repoUrl)).thenThrow(gitException);

        // When & Then - 执行方法并验证异常
        try {
            mockGitlabService.createGitlabRepository(repoUrl);
            fail("应该抛出GitException，但方法正常执行完成");
        } catch (GitException exception) {
            // 验证异常的错误代码
            assertEquals("异常代码应该是CREATE_REPOSITORY_FAIL",
                        ExceptionCode.CREATE_REPOSITORY_FAIL, exception.getExceptionCode());
            // 验证异常消息包含预期的错误信息
            assertTrue("异常消息应该包含'create repo from url fail'",
                      exception.getMessage().contains("create repo from url fail"));
        }

        // 验证方法被调用
        verify(mockGitlabService).createGitlabRepository(repoUrl);
    }

    /**
     * 测试createGitlabRepository方法处理API返回null的场景（使用分离参数）
     *
     * 测试目标：验证当GitLab API返回null时，方法能够正确处理并返回null
     */
    @Test
    public void testCreateGitlabRepository_WithSeparateParams_ReturnsNull() {
        // Given - 创建mock对象
        GitlabService mockGitlabService = mock(GitlabService.class);
        String repoBaseUrl = "https://code.trinasolar.com";
        String businessName = "itid";
        String systemSimpleName = "test186";
        String programEnName = "test-prog-1866";

        // Mock方法返回null
        when(mockGitlabService.createGitlabRepository(repoBaseUrl, businessName, systemSimpleName, programEnName))
            .thenReturn(null);

        // When - 执行被测试的方法
        Project result = mockGitlabService.createGitlabRepository(repoBaseUrl, businessName, systemSimpleName, programEnName);

        // Then - 验证结果
        assertNull("当API返回null时，方法也应该返回null", result);

        // 验证方法被调用
        verify(mockGitlabService).createGitlabRepository(repoBaseUrl, businessName, systemSimpleName, programEnName);
    }

    /**
     * 测试createGitlabRepository方法处理API返回null的场景（使用完整URL）
     *
     * 测试目标：验证当GitLab API返回null时，方法能够正确处理并返回null
     */
    @Test
    public void testCreateGitlabRepository_WithFullUrl_ReturnsNull() {
        // Given - 创建mock对象
        GitlabService mockGitlabService = mock(GitlabService.class);
        String repoUrl = "https://code.trinasolar.com/itid/test186/test-prog-1866.git";

        // Mock方法返回null
        when(mockGitlabService.createGitlabRepository(repoUrl)).thenReturn(null);

        // When - 执行被测试的方法
        Project result = mockGitlabService.createGitlabRepository(repoUrl);

        // Then - 验证结果
        assertNull("当API返回null时，方法也应该返回null", result);

        // 验证方法被调用
        verify(mockGitlabService).createGitlabRepository(repoUrl);
    }

    /**
     * 测试真实的GitlabUtil实例创建
     * 验证GitlabUtil类能够正常实例化
     */
    @Test
    public void testGitlabUtilInstantiation() {
        // When - 创建GitlabUtil实例
        GitlabService gitlabService = new GitlabService();

        // Then - 验证实例创建成功
        assertNotNull("GitlabUtil实例应该能够正常创建", gitlabService);
    }

    /**
     * 测试GitlabService的基本方法存在性
     * 验证createGitlabRepository方法存在且可调用（通过反射）
     */
    @Test
    public void testCreateGitlabRepositoryMethodExists() {
        try {
            // Given - 获取方法
            GitlabService gitlabService = new GitlabService();

            // 验证分离参数的方法
            java.lang.reflect.Method method1 = gitlabService.getClass()
                .getDeclaredMethod("createGitlabRepository", String.class, String.class, String.class, String.class);

            // 验证完整URL的方法
            java.lang.reflect.Method method2 = gitlabService.getClass()
                .getDeclaredMethod("createGitlabRepository", String.class);

            // Then - 验证方法存在
            assertNotNull("createGitlabRepository(4参数)方法应该存在", method1);
            assertNotNull("createGitlabRepository(1参数)方法应该存在", method2);

            assertEquals("方法返回类型应该是Project",
                        Project.class, method1.getReturnType());
            assertEquals("方法返回类型应该是Project",
                        Project.class, method2.getReturnType());

            // 验证参数类型
            assertEquals("第一个方法应该有4个参数", 4, method1.getParameterTypes().length);
            assertEquals("第二个方法应该有1个参数", 1, method2.getParameterTypes().length);

        } catch (NoSuchMethodException e) {
            fail("createGitlabRepository方法不存在: " + e.getMessage());
        }
    }

    /**
     * 测试GitException的基本功能
     * 验证GitException能够正确创建和携带错误信息
     */
    @Test
    public void testGitExceptionCreation() {
        // Given - 准备异常信息
        ExceptionCode code = ExceptionCode.CREATE_REPOSITORY_FAIL;
        String message = "测试异常消息";

        // When - 创建GitException
        GitException exception = new GitException(code, message);

        // Then - 验证异常属性
        assertNotNull("GitException应该能够正常创建", exception);
        assertEquals("异常代码应该正确设置", code, exception.getExceptionCode());
        assertEquals("异常消息应该正确设置", message, exception.getMessage());
    }
}
