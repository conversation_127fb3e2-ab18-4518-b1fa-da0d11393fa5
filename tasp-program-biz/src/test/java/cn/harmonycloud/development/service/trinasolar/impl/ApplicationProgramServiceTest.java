package cn.harmonycloud.development.service.trinasolar.impl;

import cn.harmonycloud.development.execption.thgn.AppException;
import org.junit.jupiter.api.Test;

import java.lang.reflect.Method;

import static org.junit.jupiter.api.Assertions.*;

/**
 * ApplicationProgramServiceImpl getPushGitUrl 方法测试类
 * 主要测试 getPushGitUrl 方法中的GitLab命名规范验证
 */
class ApplicationProgramServiceTest {

    @Test
    void testGetPushGitUrl_ValidProgramNameEn_ShouldReturnCorrectUrl() throws Exception {
        // Given
        String programNameEn = "test-prog-1866";
        String businessDomain = "itid";
        String pushGitUrlTmp = "https://code.trinasolar.com/${group}/${url}.git";
        String systemEnSimpleName = "test186";
        
        // When
        String result = callGetPushGitUrl(programNameEn, businessDomain, pushGitUrlTmp, systemEnSimpleName);
        
        // Then
        assertEquals("https://code.trinasolar.com/itid/test186/test-prog-1866.git", result);
    }

    @Test
    void testGetPushGitUrl_ProgramNameEnWithChinese_ShouldThrowException() {
        // Given
        String programNameEn = "测试应用-app";
        String businessDomain = "business";
        String pushGitUrlTmp = "https://gitlab.example.com/{GROUP}/{URL}.git";
        String systemEnSimpleName = "system";
        
        // When & Then
        AppException exception = assertThrows(AppException.class, () -> {
            callGetPushGitUrl(programNameEn, businessDomain, pushGitUrlTmp, systemEnSimpleName);
        });
        
        assertTrue(exception.getMessage().contains("应用程序英文名称格式错误"));
        assertTrue(exception.getMessage().contains(programNameEn));
    }

    @Test
    void testGetPushGitUrl_ProgramNameEnWithSpecialCharacters_ShouldThrowException() {
        // Given
        String programNameEn = "test@app#123";
        String businessDomain = "business";
        String pushGitUrlTmp = "https://gitlab.example.com/{GROUP}/{URL}.git";
        String systemEnSimpleName = "system";
        
        // When & Then
        AppException exception = assertThrows(AppException.class, () -> {
            callGetPushGitUrl(programNameEn, businessDomain, pushGitUrlTmp, systemEnSimpleName);
        });
        
        assertTrue(exception.getMessage().contains("应用程序英文名称格式错误"));
        assertTrue(exception.getMessage().contains(programNameEn));
    }

    @Test
    void testGetPushGitUrl_ProgramNameEnStartingWithHyphen_ShouldThrowException() {
        // Given
        String programNameEn = "-test-app";
        String businessDomain = "business";
        String pushGitUrlTmp = "https://gitlab.example.com/{GROUP}/{URL}.git";
        String systemEnSimpleName = "system";
        
        // When & Then
        AppException exception = assertThrows(AppException.class, () -> {
            callGetPushGitUrl(programNameEn, businessDomain, pushGitUrlTmp, systemEnSimpleName);
        });
        
        assertTrue(exception.getMessage().contains("应用程序英文名称格式错误"));
    }

    @Test
    void testGetPushGitUrl_ProgramNameEnEndingWithHyphen_ShouldThrowException() {
        // Given
        String programNameEn = "test-app-";
        String businessDomain = "business";
        String pushGitUrlTmp = "https://gitlab.example.com/{GROUP}/{URL}.git";
        String systemEnSimpleName = "system";
        
        // When & Then
        AppException exception = assertThrows(AppException.class, () -> {
            callGetPushGitUrl(programNameEn, businessDomain, pushGitUrlTmp, systemEnSimpleName);
        });
        
        assertTrue(exception.getMessage().contains("应用程序英文名称格式错误"));
    }

    @Test
    void testGetPushGitUrl_ProgramNameEnWithUnderscores_ShouldPass() throws Exception {
        // Given
        String programNameEn = "test_app_123";
        String businessDomain = "business";
        String pushGitUrlTmp = "https://gitlab.example.com/{GROUP}/{URL}.git";
        String systemEnSimpleName = "system";
        
        // When
        String result = callGetPushGitUrl(programNameEn, businessDomain, pushGitUrlTmp, systemEnSimpleName);
        
        // Then
        assertEquals("https://gitlab.example.com/business/system/test_app_123.git", result);
    }

    @Test
    void testGetPushGitUrl_ProgramNameEnEndingWithGit_ShouldThrowException() {
        // Given
        String programNameEn = "test-app.git";
        String businessDomain = "business";
        String pushGitUrlTmp = "https://gitlab.example.com/{GROUP}/{URL}.git";
        String systemEnSimpleName = "system";
        
        // When & Then
        AppException exception = assertThrows(AppException.class, () -> {
            callGetPushGitUrl(programNameEn, businessDomain, pushGitUrlTmp, systemEnSimpleName);
        });
        
        assertTrue(exception.getMessage().contains("应用程序英文名称格式错误"));
    }

    @Test
    void testGetPushGitUrl_ProgramNameEnTooLong_ShouldThrowException() {
        // Given - 创建一个超过100个字符的名称
        String programNameEn = "a".repeat(101);
        String businessDomain = "business";
        String pushGitUrlTmp = "https://gitlab.example.com/{GROUP}/{URL}.git";
        String systemEnSimpleName = "system";
        
        // When & Then
        AppException exception = assertThrows(AppException.class, () -> {
            callGetPushGitUrl(programNameEn, businessDomain, pushGitUrlTmp, systemEnSimpleName);
        });
        
        assertTrue(exception.getMessage().contains("应用程序英文名称格式错误"));
    }

    @Test
    void testGetPushGitUrl_EmptyProgramNameEn_ShouldThrowException() {
        // Given
        String programNameEn = "";
        String businessDomain = "business";
        String pushGitUrlTmp = "https://gitlab.example.com/{GROUP}/{URL}.git";
        String systemEnSimpleName = "system";
        
        // When & Then
        AppException exception = assertThrows(AppException.class, () -> {
            callGetPushGitUrl(programNameEn, businessDomain, pushGitUrlTmp, systemEnSimpleName);
        });
        
        assertTrue(exception.getMessage().contains("应用程序英文名称格式错误"));
    }

    /**
     * 使用反射调用私有的 getPushGitUrl 方法
     */
    private String callGetPushGitUrl(String programNameEn, String businessDomain, String pushGitUrlTmp, String systemEnSimpleName) throws Exception {
        Method method = ApplicationProgramServiceImpl.class.getDeclaredMethod("getPushGitUrl", 
                String.class, String.class, String.class, String.class);
        method.setAccessible(true);
        return (String) method.invoke(null, programNameEn, businessDomain, pushGitUrlTmp, systemEnSimpleName);
    }
}
